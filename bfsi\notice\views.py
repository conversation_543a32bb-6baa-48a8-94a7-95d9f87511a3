import json
import math
import time
import traceback
from django.shortcuts import get_object_or_404
import pytz
import requests
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from django.utils import timezone
from notice.whatsapp_helper import send_whatsapp_message_test, send_whatsapp_pfl, upload_media_to_whatsapp
from sendgridlogs.models import SendgridEventEntry, SendgridMail
from odr.permissions import IsAdmin, IsClientAndSelf, IsAdminOrClient, IsAnyRole
from bfsi.settings import BASE_DIR, CREDENTIALS_DICT, DB_NAME, GCP_PROJECT_ID, GCS_BUCKET_NAME, PUBSUB_EMAILS_TOPIC, PUBSUB_PDF_SPLITTER_TOPIC, PUBSUB_WA_REPORT_TOPIC, PUBSUB_WHATSAPP_MESSAGES_TOPIC, TIME_ZONE
from notice.models import CampaignTemplate, Entry, Campaign, WhatsAppTemplate, EmailTemplate, Template, User, Notice
from notice.serializers import (WhatsAppTemplateSerializer, EmailTemplateSerializer, TemplateSerializer, CampaignDetailSerializer)
from django.db.models import Sum, Count, Avg, F, ExpressionWrapper, FloatField, Q
from odr.models import Dispute, Profile, ProfileType
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.http import HttpResponse, StreamingHttpResponse, FileResponse
from django.db.models import Count
from odr.helperfunctions.gcs import gcs_manager
import io
import pandas as pd
from datetime import timedelta
from google.cloud import pubsub_v1
import logging
import os
from google.oauth2 import service_account
from django.conf import settings
from odr.utils import db_logger
from datetime import datetime
from io import BytesIO
from google.cloud import pubsub_v1
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import IsAuthenticated
from google.cloud import storage
from concurrent.futures import ThreadPoolExecutor, as_completed
import zipfile
from django.http import StreamingHttpResponse
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from rest_framework.response import Response
import os
import zipfile
import io
import tempfile
from django.http import StreamingHttpResponse, FileResponse
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from rest_framework.response import Response
import os
import zipfile
import tempfile
import threading
import concurrent.futures
import time
from django.utils.html import escape
from odr.mailhelpers import send_email, send_pfl_termination_notice
import pypdf
from notice.validation_utils import validate_profile_campaign,validate_profile_dispute

class TriggerRenamingAndSplittingView(APIView):
    parser_classes = (MultiPartParser, FormParser)
    permission_classes = [IsAuthenticated, IsAnyRole]
    # authentication_classes = [JSONWebTokenAuthentication]

    def post(self, request, *args, **kwargs):
        campaign_id = request.data.get('campaign_id')
        pdf_file = request.data.get('pdf_file')
        num_pages_per_pdf = request.data.get('num_pages_per_pdf')
        template_id = request.data.get('template_id')

        if not all([campaign_id, pdf_file, num_pages_per_pdf, template_id]):
            return Response({'error': 'campaign_id, pdf_file, num_pages_per_pdf and template_id are required'},
                            status=status.HTTP_400_BAD_REQUEST)

        try:
            num_pages_per_pdf = int(num_pages_per_pdf)
        except ValueError:
            return Response({'error': 'num_pages_per_pdf must be an integer'},
                            status=status.HTTP_400_BAD_REQUEST)

        try:
            campaign = Campaign.objects.get(id=campaign_id)
        except Campaign.DoesNotExist:
            return Response({'error': 'Invalid campaign_id'}, status=status.HTTP_400_BAD_REQUEST)

        # Check user's profile type
        user = request.user
        current_profile = get_object_or_404(Profile,user=user)
        if not validate_profile_campaign(current_profile,campaign):
            return Response({'error': 'You do not have permission to access this campaign'},
                               status=status.HTTP_403_FORBIDDEN) 

        excel_file = gcs_manager.read_file(campaign.excel_file_name)
        excel_file = io.BytesIO(excel_file)
        df = pd.read_excel(excel_file, engine='openpyxl')
        excel_file.seek(0)

        # Count rows in Excel (this will be the number of splits)
        row_count = len(df)

        #Notices will not get sent for closed disputes. So remove them from required_pages
        closed_disputes_count = Dispute.objects.filter(campaign=campaign,status='closed').count()
        required_pages = (row_count - closed_disputes_count) * num_pages_per_pdf

        pdf_reader = pypdf.PdfReader(pdf_file)
        actual_pages = len(pdf_reader.pages)

        if actual_pages != required_pages:
            return Response({
                'error': f'PDF has {actual_pages} pages, but needs {required_pages} pages for {row_count - closed_disputes_count} cases with {num_pages_per_pdf} pages each.Note: Do not upload pages for closed disputes.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate file name match
        excel_file_name = campaign.excel_file_name.split('/')[-1]
        pdf_file_name = pdf_file.name
        # if excel_file_name.split('.')[0] != pdf_file_name.split('.')[0]:
        #     return Response({'error': 'Excel and PDF files must have the same name'},
        #                     status=status.HTTP_400_BAD_REQUEST)

        # Update template with initial values
        template = Template.objects.filter(id=template_id).first()
        template.total_splits_expected = row_count
        template.splits_processed = 0
        template.splitting_status = 'processing'
        template.splitting_errors = []
        template.save()

        pdf_file_path = f'notices/{campaign_id}/{pdf_file_name}'
        gcs_manager.upload_file(pdf_file_path, pdf_file)
        template.pdf_file_name = pdf_file_path
        template.pdf_file_uploaded = True
        template.save()

        # insert data to CampaignTemplate table
        campaign_template = CampaignTemplate.objects.create(
            campaign=campaign,
            template=template
        )
        campaign_template.save()

        # Send message to Pub/Sub to trigger the Cloud Function
        try:
            message_id = self.publish_to_pubsub(
                pdf_file_name=pdf_file_name,
                excel_file_name=excel_file_name,
                num_pages_per_pdf=num_pages_per_pdf,
                email=request.user.email,
                campaign_id=campaign_id,
                template_id=template_id,
                template_name=template.name,
                user_id=request.user.id
            )
            return Response({
                'message': 'Files uploaded successfully and processing triggered',
                'pdf_file': pdf_file_name,
                'excel_file': excel_file_name,
                'message_id': message_id,
                'has_co_borrower': campaign.has_co_borrowers
            }, status=status.HTTP_202_ACCEPTED)
        except Exception as e:
            logging.error(f"Pubsub publish error: {str(e)}")
            return Response({
                'message': 'Files uploaded successfully but processing failed to trigger',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


    def publish_to_pubsub(self, pdf_file_name, excel_file_name, num_pages_per_pdf, email, campaign_id, template_id, template_name, user_id):
        """Publish message to Pub/Sub topic with explicit credentials"""
        # Pub/Sub topic details from settings
        project_id = GCP_PROJECT_ID
        topic_id = PUBSUB_PDF_SPLITTER_TOPIC
        bucket_name = GCS_BUCKET_NAME

        try:
            # Check for explicit credential file in environment
            credentials = service_account.Credentials.from_service_account_info(settings.CREDENTIALS_DICT)
            publisher = pubsub_v1.PublisherClient(credentials=credentials)
            topic_path = publisher.topic_path(project_id, topic_id)

            # Create message data
            message_data = {
                'pdf_file': pdf_file_name,
                'excel_file': excel_file_name,
                'num_pages_per_pdf': num_pages_per_pdf,
                'bucket_name': bucket_name,
                'recipient_email': email,
                'campaign_id': campaign_id,
                'template_id': template_id,
                'template_name': template_name,
                'user_id': user_id
            }

            # Convert message to bytes
            message_bytes = json.dumps(message_data).encode('utf-8')

            # Publish message
            future = publisher.publish(topic_path, data=message_bytes,timeout=1000)
            message_id = future.result()

            print(f"Published message with ID: {message_id}")
            return message_id

        except Exception as e:
            print(f"Error publishing to Pub/Sub: {str(e)}")
            raise


def template_splitting_stream(request, template_id):
    def event_stream():
        last_update = None
        while True:
            template = Template.objects.get(id=template_id)
            current_state = {
                'processed': template.splits_processed,
                'total': template.total_splits_expected,
                'status': template.splitting_status,
                'errors': template.splitting_errors
            }
            if current_state != last_update:
                yield f"data: {json.dumps(current_state)}\n\n"
                last_update = current_state
            if template.splitting_status in ['completed', 'failed'] or template.renaming_and_splitting_done:
                break
            time.sleep(1)  # Check every second
    return StreamingHttpResponse(event_stream(), content_type='text/event-stream')


class GeneratePresignedURLView(APIView):
    permission_classes = [IsAuthenticated, IsAnyRole]

    def get(self, request, campaign_id=None , template_id=None):
        if campaign_id is None:
            return Response({'error': 'campaign_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Check if user is client
        user = request.user
        current_profile = get_object_or_404(Profile,user=user)
        
        # Get campaign
        campaign = get_object_or_404(Campaign, id=campaign_id)

        if not validate_profile_campaign(current_profile,campaign):
            return Response({'error': 'You do not have permission to access this campaign'},
                               status=status.HTTP_403_FORBIDDEN)   

        excel_file_name = campaign.excel_file_name.split('/')[-1]  # Assuming the structure is 'notices/campaign_id/file_name.xlsx'
        folder_name = excel_file_name.split('.xlsx')[0]

        # Download the existing Excel file from GCS
        excel_file_path = campaign.excel_file_name

        template = Template.objects.get(id=template_id)
        template_name = template.name

        # Define the new path in GCS to upload modified file
        output_excel_path = f'notices/{campaign_id}/{template_id}/presigned_url/{folder_name}_presigned_urls.xlsx'
        # # Check if presigned_file already exists by listing blobs under exact path
        # try:
        #     existing_blobs = gcs_manager.list_files(prefix=output_excel_path)
        #     if output_excel_path in existing_blobs:
        #         signed_url = gcs_manager.generate_signed_url(output_excel_path, expiration_seconds=3600)
        #         return Response({'signed_url': signed_url}, status=status.HTTP_200_OK)
        # except Exception as e:
        #     return Response({'error': f'Error checking zip existence: {e}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        try:
            excel_data = gcs_manager.read_file(excel_file_path)
            # Read the existing Excel file
            excel_bytes = io.BytesIO(excel_data)
            df = pd.read_excel(excel_bytes)
            print("Successfully read data from Excel file")
        except Exception as e:
            print(f"Error reading Excel file: {str(e)}")
            return Response({'error': f'Failed to read Excel file: {str(e)}'},
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Define the function to generate signed URLs
        def generate_signed_link(loan_id):
            if not loan_id:  # Handle empty cell values
                print(f"Warning: Empty loan_id encountered")
                return None
            blob_name = f"{loan_id}.pdf"
            try:
                # Generate a signed URL that expires in 7 days
                file_path = f'notices/{campaign_id}/{template_id}/{template_name}/{blob_name}'
                url = gcs_manager.generate_signed_url(file_path, expiration_seconds=3600*24*7)
                return url
            except Exception as e:
                print(f"Error generating signed URL for {blob_name}: {str(e)}")
                return None

        # Add a new column 'presigned_url' with the generated links
        df['presigned_url'] = df['Loan ID'].apply(generate_signed_link)

        # Count successes and failures
        successful_urls = df['presigned_url'].notna().sum()
        failed_urls = df['presigned_url'].isna().sum()
        print(f"Generated {successful_urls} URLs successfully, {failed_urls} failed")

        # Save the modified DataFrame to an Excel file
        output = io.BytesIO()
        try:
            with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name='Sheet1', index=False)
            output.seek(0)
        except Exception as e:
            return Response({'error': f'Failed to write Excel file: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Define the new path in GCS to upload modified file
        # output_excel_path = f'notices/{campaign_id}/presigned_url/{folder_name}_presigned_urls.xlsx'

        try:
            gcs_manager.upload_file_with_content_type_and_disiposition(
                path=output_excel_path,
                data=output,
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                content_disposition=f'attachment; filename="{folder_name}_presigned_urls.xlsx"'
            )
        except Exception as e:
            return Response({'error': f'Failed to upload modified Excel: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        try:
            signed_url = gcs_manager.generate_signed_url(output_excel_path, expiration_seconds=3600)
        except Exception as e:
            return Response({'error': f'Failed to generate signed URL: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({'signed_url': signed_url}, status=status.HTTP_200_OK)


class DownloadDocumentsZipView(APIView):
    permission_classes = [IsAuthenticated, IsAnyRole]

    def get(self, request, campaign_id=None, template_id=None):
        if not campaign_id:
            return Response({'error': 'campaign_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Check user's profile type
        user = request.user
        current_profile = get_object_or_404(Profile,user=user)        

        # Get campaign
        campaign = Campaign.objects.filter(id=campaign_id).first()
        if not campaign:
            return Response({'error': 'Invalid campaign_id'}, status=status.HTTP_400_BAD_REQUEST)

        if not validate_profile_campaign(current_profile,campaign):
            return Response({'error': 'You do not have permission to access this campaign'},
                               status=status.HTTP_403_FORBIDDEN)     

        # Get folder path from Excel file name
        template = Template.objects.get(id=template_id)
        template_name = template.name

        excel_file_path = campaign.excel_file_name
        folder_name = os.path.splitext(os.path.basename(excel_file_path))[0]
        folder_path = f'notices/{campaign_id}/{template_id}/{template_name}/'
        zip_blob_path = f'notices/{campaign_id}/{template_id}/zips/{folder_name}.zip'

        # Check if ZIP already exists by listing blobs under exact path
        try:
            existing_blobs = gcs_manager.list_files(prefix=zip_blob_path)
            if zip_blob_path in existing_blobs:
                signed_url = gcs_manager.generate_signed_url(zip_blob_path, expiration_seconds=3600)
                return Response({'zip_url': signed_url}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': f'Error checking zip existence: {e}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # List files in GCS under that folder
        try:
            blob_list = gcs_manager.list_files(prefix=folder_path)
            pdf_files = [blob for blob in blob_list if blob.endswith('.pdf')]
        except Exception as e:
            return Response({'error': f'Error listing files in folder: {e}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        if not pdf_files:
            return Response({'error': 'No PDF files found in folder.'}, status=status.HTTP_400_BAD_REQUEST)

        # Create a streaming response
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        temp_filename = temp_file.name
        temp_file.close()  # Close it so we can reopen it in the thread

        # Start a background thread to create the zip file
        # This helps prevent timeouts when dealing with large archives
        zip_thread = threading.Thread(
            target=self._create_zip_file,
            args=(temp_filename, pdf_files)
        )
        zip_thread.daemon = True
        zip_thread.start()

        # Wait for the zip creation to finish or timeout
        timeout = 600  # 10 minute timeout
        start_time = time.time()

        while zip_thread.is_alive():
            if time.time() - start_time > timeout:
                # If it takes too long, return an error
                os.unlink(temp_filename)
                return Response(
                    {'error': 'Zip file creation timed out. Please try with fewer files.'},
                    status=status.HTTP_408_REQUEST_TIMEOUT
                )
            time.sleep(0.5)  # Check every half second

        # Once the thread is done, serve the file
        try:
            with open(temp_filename, 'rb') as f:
                gcs_manager.upload_file_with_content_type_and_disiposition(
                    path=zip_blob_path,
                    data=f,
                    content_type='application/zip',
                    content_disposition=f'attachment; filename="{folder_name}.zip"'
                )
            # Generate signed URL
            signed_url = gcs_manager.generate_signed_url(zip_blob_path, expiration_seconds=3600)
            # Delete temp zip
            os.unlink(temp_filename)
            return Response({'zip_url': signed_url}, status=status.HTTP_200_OK)

        except Exception as e:
            try:
                os.unlink(temp_filename)
            except:
                pass
            return Response(
                {'error': f'Error serving zip file: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _create_zip_file(self, zip_filename, file_paths):
        """Create a zip file with parallel file downloads"""
        # Use a thread pool to download files in parallel
        MAX_WORKERS = 60  # Adjust based on your server capacity
        BATCH_SIZE = 100  # Process files in batches

        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zip_file:
            # Process in batches to avoid overwhelming memory
            for i in range(0, len(file_paths), BATCH_SIZE):
                batch = file_paths[i:i + BATCH_SIZE]
                file_data = {}  # Store downloaded content temporarily

                # Download files in parallel
                with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
                    # Create download tasks
                    future_to_file = {
                        executor.submit(gcs_manager.read_file, blob_name): blob_name
                        for blob_name in batch
                    }

                    # Process results as they complete
                    for future in concurrent.futures.as_completed(future_to_file):
                        blob_name = future_to_file[future]
                        try:
                            data = future.result()
                            short_name = os.path.basename(blob_name)
                            file_data[short_name] = data
                        except Exception as e:
                            print(f"Error downloading {blob_name}: {e}")

                # Add the downloaded files to the zip
                for short_name, data in file_data.items():
                    try:
                        zip_file.writestr(short_name, data)
                    except Exception as e:
                        print(f"Error adding {short_name} to zip: {e}")

                # Clear the temporary data to free memory
                file_data.clear()

############ working code with nested folder's download approach ############
# class DownloadDocumentsZipView(APIView):
#     permission_classes = [IsAuthenticated, IsAnyRole]

#     def get(self, request, campaign_id=None, template_id=None):
#         if not campaign_id:
#             return Response({'error': 'campaign_id is required'}, status=status.HTTP_400_BAD_REQUEST)

#         # Check user's profile type
#         user = request.user
#         is_admin = Profile.objects.filter(user=user, profile_type=ProfileType.admin.name).exists()
#         is_client = Profile.objects.filter(user=user, profile_type=ProfileType.client.name).exists()
#         is_sub_client = Profile.objects.filter(user=user, profile_type=ProfileType.sub_client.name).exists()
#         is_case_manager = Profile.objects.filter(user=user, profile_type=ProfileType.case_manager.name).exists()
#         is_arbitrator = Profile.objects.filter(user=user, profile_type=ProfileType.arbitrator.name).exists()

#         # Get campaign
#         campaign = Campaign.objects.filter(id=campaign_id).first()
#         if not campaign:
#             return Response({'error': 'Invalid campaign_id'}, status=status.HTTP_400_BAD_REQUEST)

#         # Check permissions based on user's profile type
#         if is_admin:
#             # Admin can access any campaign
#             pass
#         elif is_client or is_sub_client:
#             # Client and sub_client can only access their campaigns
#             client_profile = Profile.objects.get(user=user, profile_type__in=[ProfileType.client.name, ProfileType.sub_client.name])
#             if not campaign.client or campaign.client.id != client_profile.id:
#                 return Response({'error': 'You do not have permission to access this campaign'},
#                                status=status.HTTP_403_FORBIDDEN)
#         elif is_case_manager:
#             # Case manager can only access campaigns for disputes they're assigned to
#             case_manager_profile = Profile.objects.get(user=user, profile_type=ProfileType.case_manager.name)
#             # Check if any disputes in this campaign are assigned to this case manager
#             campaign_disputes = Dispute.objects.filter(campaign=campaign)
#             has_access = False
#             for dispute in campaign_disputes:
#                 if dispute.case_manager_rv.filter(id=case_manager_profile.id).exists():
#                     has_access = True
#                     break
#             if not has_access:
#                 return Response({'error': 'You do not have permission to access this campaign'},
#                                status=status.HTTP_403_FORBIDDEN)
#         elif is_arbitrator:
#             # Arbitrator can only access campaigns for disputes they're assigned to
#             arbitrator_profile = Profile.objects.get(user=user, profile_type=ProfileType.arbitrator.name)
#             # Check if any disputes in this campaign are assigned to this arbitrator
#             campaign_disputes = Dispute.objects.filter(campaign=campaign)
#             has_access = False
#             for dispute in campaign_disputes:
#                 if dispute.arbitrator_rv.filter(id=arbitrator_profile.id).exists():
#                     has_access = True
#                     break
#             if not has_access:
#                 return Response({'error': 'You do not have permission to access this campaign'},
#                                status=status.HTTP_403_FORBIDDEN)

#         # Get folder path from Excel file name
#         template = Template.objects.get(id=template_id)
#         template_name = template.name

#         excel_file_path = campaign.excel_file_name
#         folder_name = os.path.splitext(os.path.basename(excel_file_path))[0]
#         folder_path = f'notices/{campaign_id}/{template_id}/{template_name}/'
#         zip_blob_path = f'notices/{campaign_id}/{template_id}/zips/include_nested_folders_{folder_name}.zip'

#         # Check if ZIP already exists by listing blobs under exact path
#         try:
#             existing_blobs = gcs_manager.list_files(prefix=zip_blob_path)
#             if zip_blob_path in existing_blobs:
#                 signed_url = gcs_manager.generate_signed_url(zip_blob_path, expiration_seconds=3600)
#                 return Response({'zip_url': signed_url}, status=status.HTTP_200_OK)
#         except Exception as e:
#             return Response({'error': f'Error checking zip existence: {e}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

#         # List files in GCS under that folder recursively, including nested folders
#         try:
#             # Use the new recursive method to get all PDF files
#             pdf_files = gcs_manager.list_files_recursively(prefix=folder_path, file_extension='.pdf')
#         except Exception as e:
#             return Response({'error': f'Error listing files in folder: {e}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

#         if not pdf_files:
#             return Response({'error': 'No PDF files found in folder.'}, status=status.HTTP_400_BAD_REQUEST)

#         # Create a streaming response
#         temp_file = tempfile.NamedTemporaryFile(delete=False)
#         temp_filename = temp_file.name
#         temp_file.close()  # Close it so we can reopen it in the thread

#         # Start a background thread to create the zip file
#         # This helps prevent timeouts when dealing with large archives
#         zip_thread = threading.Thread(
#             target=self._create_zip_file,
#             args=(temp_filename, pdf_files, folder_path)
#         )
#         zip_thread.daemon = True
#         zip_thread.start()

#         # Wait for the zip creation to finish or timeout
#         timeout = 600  # 10 minute timeout
#         start_time = time.time()

#         while zip_thread.is_alive():
#             if time.time() - start_time > timeout:
#                 # If it takes too long, return an error
#                 os.unlink(temp_filename)
#                 return Response(
#                     {'error': 'Zip file creation timed out. Please try with fewer files.'},
#                     status=status.HTTP_408_REQUEST_TIMEOUT
#                 )
#             time.sleep(0.5)  # Check every half second

#         # Once the thread is done, serve the file
#         try:
#             with open(temp_filename, 'rb') as f:
#                 gcs_manager.upload_file_with_content_type_and_disiposition(
#                     path=zip_blob_path,
#                     data=f,
#                     content_type='application/zip',
#                     content_disposition=f'attachment; filename="include_nested_folders_{folder_name}.zip"'
#                 )
#             # Generate signed URL
#             signed_url = gcs_manager.generate_signed_url(zip_blob_path, expiration_seconds=24*3600)
#             # Delete temp zip
#             os.unlink(temp_filename)
#             return Response({'zip_url': signed_url}, status=status.HTTP_200_OK)

#         except Exception as e:
#             try:
#                 os.unlink(temp_filename)
#             except:
#                 pass
#             return Response(
#                 {'error': f'Error serving zip file: {str(e)}'},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )

#     def _create_zip_file(self, zip_filename, file_paths, base_folder_path):
#         """Create a zip file with parallel file downloads and nested folder support"""
#         # Use a thread pool to download files in parallel
#         MAX_WORKERS = 60  # Adjust based on your server capacity
#         BATCH_SIZE = 100  # Process files in batches

#         with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zip_file:
#             # Process in batches to avoid overwhelming memory
#             for i in range(0, len(file_paths), BATCH_SIZE):
#                 batch = file_paths[i:i + BATCH_SIZE]
#                 file_data = {}  # Store downloaded content temporarily

#                 # Download files in parallel
#                 with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
#                     # Create download tasks
#                     future_to_file = {
#                         executor.submit(gcs_manager.read_file, blob_name): blob_name
#                         for blob_name in batch
#                     }

#                     # Process results as they complete
#                     for future in concurrent.futures.as_completed(future_to_file):
#                         blob_name = future_to_file[future]
#                         try:
#                             data = future.result()
#                             # Generate the nested filename with underscores
#                             nested_filename = gcs_manager.generate_nested_filename(blob_name, base_folder_path)
#                             file_data[nested_filename] = data
#                         except Exception as e:
#                             print(f"Error downloading {blob_name}: {e}")

#                 # Add the downloaded files to the zip
#                 for nested_filename, data in file_data.items():
#                     try:
#                         zip_file.writestr(nested_filename, data)
#                     except Exception as e:
#                         print(f"Error adding {nested_filename} to zip: {e}")

#                 # Clear the temporary data to free memory
#                 file_data.clear()


# TODO remove this function as we have created cloud run function for the same functionality
class WhatsAppReportGenerator:
    def __init__(self):
        self.status_map = {
            'initiated': 'Message was initiated by the system',
            'sent': 'Message was sent from WhatsApp Business API',
            'delivered': 'Message was delivered to recipient\'s phone',
            'read': 'Recipient has read the message',
            'failed': 'Message failed to deliver'
        }

    def normalize_phone_number(self, phone_number, status):
        """
        Normalize phone numbers across different statuses
        - For 'initiated' status: Remove '91' prefix if it exists (12 digits to 10 digits)
        - For other statuses: Return the number as is (already 10 digits)
        """
        if status == 'initiated' and phone_number and phone_number.startswith('91') and len(phone_number) == 12:
            return phone_number[2:]
        return phone_number

    def generate_report(self, start_date=None, end_date=None):
        """
        Generate a comprehensive delivery report for WhatsApp messages

        Args:
            start_date: datetime object for report start date
            end_date: datetime object for report end date
        """
        # Base queryset
        queryset = Entry.objects.all()

        # Apply date filters if provided
        if start_date:
            queryset = queryset.filter(timestamp__gte=start_date)
        if end_date:
            queryset = queryset.filter(timestamp__lte=end_date)

        # Generate summary statistics
        summary = {
            'total_messages': queryset.filter(status='initiated').count(),  # Count unique initiated messages
            'status_breakdown': queryset.values('status').annotate(
                count=Count('id')
            ).order_by('status'),
            'delivery_rate': self._calculate_delivery_rate(queryset),
            'average_delivery_time': self._calculate_avg_delivery_time(queryset),
            'failed_messages': queryset.filter(status='failed').count(),
        }

        # Get all messages that were initiated
        initiated_entries = queryset.filter(status='initiated')

        # Generate detailed message history with all timestamps
        detailed_data = []
        for index, initiated_entry in enumerate(initiated_entries, 1):
            # Normalize the phone number
            normalized_phone = self.normalize_phone_number(initiated_entry.phone_number, 'initiated')

            # Initialize data for this message
            message_data = {
                'S.No': index,
                'msg_id': initiated_entry.msg_id,
                'Phone Number': normalized_phone,
                'Initiated timestamp': initiated_entry.timestamp,
                'Sent timestamp': None,
                'Delivered timestamp': None,
                'Read timestamp': None,
                'Failed timestamp': None,
                'Failed Reason': None
            }

            # Find all other status entries for this message ID
            other_entries = queryset.filter(
                msg_id=initiated_entry.msg_id
            ).exclude(
                id=initiated_entry.id
            ).order_by('timestamp')

            # Fill in timestamps for each status
            for entry in other_entries:
                # Skip entries with mismatched phone numbers (after normalization)
                entry_phone = self.normalize_phone_number(entry.phone_number, entry.status)

                if entry.status == 'sent':
                    message_data['Sent timestamp'] = entry.timestamp
                elif entry.status == 'delivered':
                    message_data['Delivered timestamp'] = entry.timestamp
                elif entry.status == 'read':
                    message_data['Read timestamp'] = entry.timestamp
                elif entry.status == 'failed':
                    message_data['Failed timestamp'] = entry.timestamp
                    message_data['Failed Reason'] = entry.failed_reason

            detailed_data.append(message_data)

        return summary, detailed_data

    def export_to_excel(self, start_date=None, end_date=None):
        """Export report to Excel file"""
        summary, detailed_data = self.generate_report(start_date, end_date)

        # Create Excel writer object
        output = io.BytesIO()
        writer = pd.ExcelWriter(output, engine='xlsxwriter')

        # Convert detailed data to DataFrame
        df_detailed = pd.DataFrame(detailed_data)

        # Define columns for the detailed report
        detailed_columns = [
            'S.No', 'Phone Number', 'Initiated timestamp', 'Sent timestamp',
            'Delivered timestamp', 'Read timestamp', 'Failed timestamp', 'Failed Reason'
        ]

        # Create empty DataFrame with necessary columns if no data
        if not detailed_data:
            df_detailed = pd.DataFrame(columns=detailed_columns)
        else:
            # Ensure all columns exist (even if empty)
            for col in detailed_columns:
                if col not in df_detailed.columns:
                    df_detailed[col] = None

            # Reorder columns to match requested format
            df_detailed = df_detailed[detailed_columns]

        # Format timestamp columns
        timestamp_columns = [col for col in df_detailed.columns if 'timestamp' in col]
        for col in timestamp_columns:
            df_detailed[col] = df_detailed[col].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) else ''
            )

        # Create summary DataFrame
        summary_data = {
            'Metric': [
                'Total Messages',
                'Delivery Rate',
                'Failed Messages',
                'Average Delivery Time'
            ],
            'Value': [
                summary['total_messages'],
                f"{summary['delivery_rate']}%",
                summary['failed_messages'],
                summary['average_delivery_time']
            ]
        }
        df_summary = pd.DataFrame(summary_data)

        # Status breakdown summary
        status_data = [{
            'Status': status['status'],
            'Description': self.status_map.get(status['status'], 'Unknown status'),
            'Count': status['count']
        } for status in summary['status_breakdown']]
        df_status = pd.DataFrame(status_data) if status_data else pd.DataFrame(columns=['Status', 'Description', 'Count'])

        # Write each DataFrame to a different worksheet
        df_summary.to_excel(writer, sheet_name='Summary', index=False)
        df_status.to_excel(writer, sheet_name='Status Breakdown', index=False)
        df_detailed.to_excel(writer, sheet_name='Message History', index=False)

        # Add formatting to the Excel file
        workbook = writer.book

        # Format for headers
        header_format = workbook.add_format({'bold': True, 'bg_color': '#D9E1F2', 'border': 1})

        # Apply header format to all sheets - fixed method that doesn't use get_row_data
        for sheet_name, df in [('Summary', df_summary), ('Status Breakdown', df_status), ('Message History', df_detailed)]:
            worksheet = writer.sheets[sheet_name]

            # Write headers with format
            for col_num, column_name in enumerate(df.columns):
                worksheet.write(0, col_num, column_name, header_format)

        # Add autofilter and freeze panes for Message History sheet
        if len(df_detailed) > 0:
            worksheet = writer.sheets['Message History']
            worksheet.autofilter(0, 0, len(df_detailed), len(df_detailed.columns) - 1)
            worksheet.freeze_panes(1, 0)  # Freeze the header row

            # Set column widths for better readability
            for i, col in enumerate(df_detailed.columns):
                max_width = max(
                    len(str(col)) + 2,
                    df_detailed[col].astype(str).str.len().max() + 2 if len(df_detailed) > 0 else 15
                )
                worksheet.set_column(i, i, min(max_width, 30))

        # Close the writer and get the output
        writer.close()
        output.seek(0)
        return output


    def _calculate_delivery_rate(self, queryset):
        """Calculate the delivery rate as a percentage"""
        initiated = queryset.filter(status='initiated').count()
        if initiated == 0:
            return 0

        # Count unique message IDs that have either delivered or read status
        delivered_ids = queryset.filter(status__in=['delivered', 'read']).values_list('msg_id', flat=True).distinct()
        delivered_count = len(delivered_ids)

        return round((delivered_count / initiated) * 100, 2)

    def _calculate_avg_delivery_time(self, queryset):
        """Calculate average time between sent and delivered status"""
        # Get message IDs that have both sent and delivered/read status
        sent_msg_ids = queryset.filter(status='sent').values_list('msg_id', flat=True).distinct()
        delivered_msg_ids = queryset.filter(status__in=['delivered', 'read']).values_list('msg_id', flat=True).distinct()

        # Find intersection of these IDs
        valid_msg_ids = set(sent_msg_ids).intersection(set(delivered_msg_ids))

        if not valid_msg_ids:
            return "N/A"

        total_time = timedelta()
        count = 0

        for msg_id in valid_msg_ids:
            # Get sent and delivered timestamps for this message
            sent_entry = queryset.filter(msg_id=msg_id, status='sent').order_by('timestamp').first()
            delivered_entry = queryset.filter(msg_id=msg_id, status__in=['delivered', 'read']).order_by('timestamp').first()

            if sent_entry and delivered_entry:
                delivery_time = delivered_entry.timestamp - sent_entry.timestamp
                if delivery_time.total_seconds() >= 0:  # Ensure valid time difference
                    total_time += delivery_time
                    count += 1

        if count == 0:
            return "N/A"

        avg_time = total_time / count
        # Format the timedelta in a more readable format (hours:minutes:seconds)
        total_seconds = avg_time.total_seconds()
        hours, remainder = divmod(total_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)

        if hours > 0:
            return f"{int(hours)}h {int(minutes)}m {int(seconds)}s"
        elif minutes > 0:
            return f"{int(minutes)}m {int(seconds)}s"
        else:
            return f"{seconds:.2f}s"


# View function
def download_report(request):
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Convert string dates to datetime objects
    if start_date:
        start_date = timezone.datetime.strptime(start_date, '%Y-%m-%d').replace(
            tzinfo=timezone.get_current_timezone())
    if end_date:
        # Set end_date to end of day
        end_date = timezone.datetime.strptime(end_date, '%Y-%m-%d').replace(
            tzinfo=timezone.get_current_timezone())
        end_date = end_date.replace(hour=23, minute=59, second=59)

    # Generate report
    report_generator = WhatsAppReportGenerator()
    excel_file = report_generator.export_to_excel(start_date, end_date)

    # Create the HTTP response
    response = HttpResponse(
        excel_file.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    filename = f"whatsapp_delivery_report_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    response['Content-Disposition'] = f'attachment; filename={filename}'

    return response


# class WhatsappWebhookView(APIView):
#     authentication_classes = []
#     permission_classes = []

#     def get(self, request):
#         db_logger.warning(
#             f'Whatsapp webhook calledd : \nself : {self}\nrequest : {request}\nself.request.query_params : {self.request.query_params}')

#         challenge = int(self.request.query_params.get('hub.challenge', ''))
#         verify_token = self.request.query_params.get('hub.verify_token', '')

#         if verify_token == 'webnyay1234':
#             return Response(data=challenge)
#         return Response(data={}, status=status.HTTP_400_BAD_REQUEST)

#     def post(self, request):
#         db_logger.warning(
#             f'Whatsapp webhook calledd : \nself : {self}\nrequest : {request}\nrequest.data : {request.data}')
#         try:
#             if request.data["object"] == "whatsapp_business_account":
#                 for entry in request.data["entry"]:
#                     if entry["id"] == "***************":
#                         for change in entry["changes"]:
#                             if change["field"] == "messages":
#                                 if change["value"]["messaging_product"] == "whatsapp":
#                                     for status in change["value"]["statuses"]:
#                                         try:
#                                             timestamp = timezone.make_aware(datetime.fromtimestamp(
#                                                 int(status["timestamp"])), pytz.timezone(TIME_ZONE))
#                                             if len(status["recipient_id"]) == 12:
#                                                 phone_number = status["recipient_id"][2:]
#                                             else:
#                                                 phone_number = status["recipient_id"]
#                                             # user = User.objects.filter(
#                                             #     phone_number__contains=phone_number).first()
#                                             auth_user = Profile.objects.filter(
#                                                 phone_number__contains=phone_number).first()
#                                             # if user:
#                                             #     entry = Entry.objects.create(
#                                             #         msg_id=status["id"], phone_number=phone_number, status=status["status"], timestamp=timestamp, user=user)
#                                             if auth_user:
#                                                 user_instance = User.objects.get(id=auth_user.user_id)
#                                                 entry = Entry.objects.create(
#                                                     msg_id=status["id"], phone_number=phone_number, status=status["status"], timestamp=timestamp, user=user_instance)
#                                             else:
#                                                 entry = Entry.objects.create(
#                                                     msg_id=status["id"], phone_number=phone_number, status=status["status"], timestamp=timestamp)
#                                             if status["status"] == "failed":
#                                                 entry.failed_reason = status["errors"][0]["message"]
#                                                 entry.save()
#                                         except Exception:
#                                             traceback_str = traceback.format_exc()
#                                             print(traceback_str)
#                                             db_logger.exception(
#                                                 "Error in whatsapp webhook in this status :" + json.dumps(status) + '\n' + traceback_str)
#         except Exception:
#             traceback_str = traceback.format_exc()
#             print(traceback_str)
#             db_logger.exception("Error in whatsapp webhook" + traceback_str)

#         return Response(data={})

class WhatsappWebhookView(APIView):
    authentication_classes = []
    permission_classes = []

    def get(self, request):
        db_logger.warning(
            f'Whatsapp webhook called : \nrequest : {request}\nself.request.query_params : {self.request.query_params}')

        challenge = int(self.request.query_params.get('hub.challenge', ''))
        verify_token = self.request.query_params.get('hub.verify_token', '')

        if verify_token == 'webnyay1234':
            return Response(data=challenge)
        return Response(data={}, status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        db_logger.warning(
            f'Whatsapp webhook called : \nrequest.data : {request.data}')

        # Keep the original request data for forwarding
        original_data = request.data

        try:
            # Determine current environment
            is_prod_environment = DB_NAME != "backend-bfsi-dev"

            # Process the webhook data for current environment
            if original_data.get("object") == "whatsapp_business_account":
                for entry in original_data.get("entry", []):
                    # if entry.get("id") == "***************":
                    for change in entry.get("changes", []):
                        if change.get("field") == "messages":
                            value = change.get("value", {})
                            if value.get("messaging_product") == "whatsapp":
                                for status in value.get("statuses", []):
                                    try:
                                        timestamp = timezone.make_aware(
                                            datetime.fromtimestamp(int(status["timestamp"])),
                                            pytz.timezone(TIME_ZONE)
                                        )

                                        # Format phone number
                                        if len(status["recipient_id"]) == 12:
                                            phone_number = status["recipient_id"][2:]
                                        else:
                                            phone_number = status["recipient_id"]

                                        # Find user by phone number
                                        auth_user = Profile.objects.filter(
                                            phone_number__contains=phone_number).first()

                                        # Create the entry
                                        if auth_user:
                                            user_instance = User.objects.get(id=auth_user.user_id)
                                            entry = Entry.objects.create(
                                                msg_id=status["id"],
                                                phone_number=phone_number,
                                                status=status["status"],
                                                timestamp=timestamp,
                                                user=user_instance
                                            )
                                        else:
                                            entry = Entry.objects.create(
                                                msg_id=status["id"],
                                                phone_number=phone_number,
                                                status=status["status"],
                                                timestamp=timestamp
                                            )

                                        # Add failure reason if applicable
                                        if status["status"] == "failed" and "errors" in status and status["errors"]:
                                            entry.failed_reason = status["errors"][0]["message"]
                                            entry.save()
                                    except Exception:
                                        traceback_str = traceback.format_exc()
                                        db_logger.exception(
                                            "Error in whatsapp webhook in this status: " +
                                            json.dumps(status) + '\n' + traceback_str
                                        )

            # Forward to dev if we're in production
            if is_prod_environment:
                try:
                    dev_webhook_url = getattr(settings, 'DEV_WHATSAPP_WEBHOOK_URL',
                                              'https://bfsi-dev-api.webnyay.in/whatsappwebhook/')

                    response_dev = requests.post(
                        dev_webhook_url,
                        json=original_data,
                        timeout=10  # Add timeout to prevent hanging
                    )

                    if response_dev.status_code != 200:
                        db_logger.error(f"Failed to forward WhatsApp webhook to dev: {response_dev.status_code}")
                        # We don't return an error here so the original process continues

                except requests.RequestException as e:
                    db_logger.error(f"Error forwarding WhatsApp webhook to dev: {str(e)}")
                    # We don't return an error here so the original process continues

            return Response(data={})

        except Exception:
            traceback_str = traceback.format_exc()
            db_logger.exception("Error in whatsapp webhook: " + traceback_str)
            return Response(data={})


# TODO remove this function as we have created cloud run function for the same functionality
class CombinedReportView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        folder_name = request.data.get('folder_name')
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')

        if not folder_name:
            return Response({'error': 'folder_name is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Process date parameters for WhatsApp report
        if start_date:
            start_date = timezone.datetime.strptime(start_date, '%Y-%m-%d').replace(
                tzinfo=timezone.get_current_timezone())
        if end_date:
            # Set end_date to end of day
            end_date = timezone.datetime.strptime(end_date, '%Y-%m-%d').replace(
                tzinfo=timezone.get_current_timezone())
            end_date = end_date.replace(hour=23, minute=59, second=59)

        # Step 1: Generate WhatsApp delivery report
        report_generator = WhatsAppReportGenerator()
        whatsapp_summary, whatsapp_detailed = report_generator.generate_report(start_date, end_date)

        # Step 2: Download and process the existing Excel file with loan data
        excel_file_path = f'notices/{folder_name}.xlsx'
        try:
            excel_data = gcs_manager.read_file(excel_file_path)
            # Read the existing Excel file
            excel_bytes = io.BytesIO(excel_data)
            opportunities_df = pd.read_excel(excel_bytes)
            print("Successfully read data from Excel file")
        except Exception as e:
            print(f"Error reading Excel file: {str(e)}")
            return Response({'error': f'Failed to read Excel file: {str(e)}'},
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Generate signed URLs for each loan_id
        def generate_signed_link(loan_id):
            if not loan_id:  # Handle empty cell values
                print(f"Warning: Empty loan_id encountered")
                return None
            blob_name = f"{loan_id}.pdf"
            try:
                # Generate a signed URL that expires in 7 days
                file_path = f'notices/{folder_name}/{blob_name}'
                url = gcs_manager.generate_signed_url(file_path, expiration_seconds=3600*24*7)
                return url
            except Exception as e:
                print(f"Error generating signed URL for {blob_name}: {str(e)}")
                return None

        # Add a new column 'presigned_url' with the generated links
        opportunities_df['presigned_url'] = opportunities_df['Loan ID'].apply(generate_signed_link)

        # Count successes and failures
        successful_urls = opportunities_df['presigned_url'].notna().sum()
        failed_urls = opportunities_df['presigned_url'].isna().sum()
        print(f"Generated {successful_urls} URLs successfully, {failed_urls} failed")

        # Create WhatsApp status DataFrame for merging with opportunities
        if whatsapp_detailed:
            whatsapp_df = pd.DataFrame(whatsapp_detailed)
            # Clean and standardize phone numbers for matching
            if 'Phone Number' in whatsapp_df.columns:
                # Clean phone numbers (remove spaces, hyphens, etc.)
                whatsapp_df['Phone Number'] = whatsapp_df['Phone Number'].astype(str).str.replace(r'\D', '', regex=True)

                # Extract required columns for merging
                whatsapp_status_columns = [
                    'Phone Number',
                    'Initiated timestamp',
                    'Sent timestamp',
                    'Delivered timestamp',
                    'Read timestamp',
                    'Failed timestamp',
                    'Failed Reason'
                ]

                # Select only columns that exist in the DataFrame
                available_columns = [col for col in whatsapp_status_columns if col in whatsapp_df.columns]
                whatsapp_status_df = whatsapp_df[available_columns]
            else:
                whatsapp_status_df = pd.DataFrame(columns=[
                    'Phone Number',
                    'Initiated timestamp',
                    'Sent timestamp',
                    'Delivered timestamp',
                    'Read timestamp',
                    'Failed timestamp',
                    'Failed Reason'
                ])
        else:
            whatsapp_status_df = pd.DataFrame(columns=[
                'Phone Number',
                'Initiated timestamp',
                'Sent timestamp',
                'Delivered timestamp',
                'Read timestamp',
                'Failed timestamp',
                'Failed Reason'
            ])

        # Clean phone numbers in opportunities_df for matching
        if 'Phone' in opportunities_df.columns:
            opportunities_df['Phone_Clean'] = opportunities_df['Phone'].astype(str).str.replace(r'\D', '', regex=True)

            # Merge WhatsApp status data into opportunities dataframe
            opportunities_df = pd.merge(
                opportunities_df,
                whatsapp_status_df,
                how='left',
                left_on='Phone_Clean',
                right_on='Phone Number'
            )

            # Drop the temporary clean phone column and duplicate phone number column
            opportunities_df = opportunities_df.drop(['Phone_Clean', 'Phone Number'], axis=1, errors='ignore')

            # Format timestamp columns if they exist
            timestamp_columns = [col for col in opportunities_df.columns if 'timestamp' in col]
            for col in timestamp_columns:
                if col in opportunities_df.columns:
                    opportunities_df[col] = opportunities_df[col].apply(
                        lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) else ''
                    )

        # Step 3: Create a combined Excel file with multiple sheets
        output = io.BytesIO()
        try:
            with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                # Sheet 1: Opportunities with presigned URLs and WhatsApp status
                opportunities_df.to_excel(writer, sheet_name='Opportunities', index=False)

                # Create WhatsApp Summary sheet
                summary_data = {
                    'Metric': [
                        'Total Messages',
                        'Delivery Rate',
                        'Failed Messages',
                        'Average Delivery Time'
                    ],
                    'Value': [
                        whatsapp_summary['total_messages'],
                        f"{whatsapp_summary['delivery_rate']}%",
                        whatsapp_summary['failed_messages'],
                        whatsapp_summary['average_delivery_time']
                    ]
                }
                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name='WhatsApp Summary', index=False)

                # Create Status Breakdown sheet
                status_data = [{
                    'Status': status['status'],
                    'Description': report_generator.status_map.get(status['status'], 'Unknown status'),
                    'Count': status['count']
                } for status in whatsapp_summary['status_breakdown']]
                df_status = pd.DataFrame(status_data) if status_data else pd.DataFrame(columns=['Status', 'Description', 'Count'])
                df_status.to_excel(writer, sheet_name='Status Breakdown', index=False)

                # Create Message History sheet for detailed WhatsApp data
                detailed_columns = [
                    'S.No', 'Phone Number', 'Initiated timestamp', 'Sent timestamp',
                    'Delivered timestamp', 'Read timestamp', 'Failed timestamp', 'Failed Reason'
                ]

                # Create detailed DataFrame
                if not whatsapp_detailed:
                    df_detailed = pd.DataFrame(columns=detailed_columns)
                else:
                    df_detailed = pd.DataFrame(whatsapp_detailed)
                    # Ensure all columns exist (even if empty)
                    for col in detailed_columns:
                        if col not in df_detailed.columns:
                            df_detailed[col] = None

                    # Reorder columns to match requested format
                    df_detailed = df_detailed[detailed_columns]

                    # Format timestamp columns
                    timestamp_columns = [col for col in df_detailed.columns if 'timestamp' in col]
                    for col in timestamp_columns:
                        df_detailed[col] = df_detailed[col].apply(
                            lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) else ''
                        )

                df_detailed.to_excel(writer, sheet_name='Message History', index=False)

                # Apply formatting
                workbook = writer.book

                # Format for headers
                header_format = workbook.add_format({'bold': True, 'bg_color': '#D9E1F2', 'border': 1})

                # Apply header format to all sheets
                for sheet_name in writer.sheets:
                    worksheet = writer.sheets[sheet_name]

                    # Get column names based on the sheet
                    if sheet_name == 'Opportunities':
                        columns = opportunities_df.columns
                    elif sheet_name == 'WhatsApp Summary':
                        columns = df_summary.columns
                    elif sheet_name == 'Status Breakdown':
                        columns = df_status.columns
                    elif sheet_name == 'Message History':
                        columns = df_detailed.columns
                    else:
                        continue  # Skip if sheet name is not recognized

                    # Write headers with format
                    for col_num, column_name in enumerate(columns):
                        worksheet.write(0, col_num, column_name, header_format)

                    # Add autofilter and freeze panes
                    worksheet.autofilter(0, 0, 0, len(columns) - 1)
                    worksheet.freeze_panes(1, 0)  # Freeze the header row

                # Set column widths for Message History sheet for better readability
                if 'Message History' in writer.sheets:
                    worksheet = writer.sheets['Message History']
                    for i, col in enumerate(df_detailed.columns):
                        max_width = max(
                            len(str(col)) + 2,
                            df_detailed[col].astype(str).str.len().max() + 2 if len(df_detailed) > 0 else 15
                        )
                        worksheet.set_column(i, i, min(max_width, 30))

                # Set column widths for Opportunities sheet
                if 'Opportunities' in writer.sheets:
                    worksheet = writer.sheets['Opportunities']
                    for i, col in enumerate(opportunities_df.columns):
                        # Special handling for the URL column and timestamp columns to make them wider
                        if col == 'presigned_url':
                            worksheet.set_column(i, i, 50)
                        elif 'timestamp' in col.lower() or col == 'Failed Reason':
                            worksheet.set_column(i, i, 25)
                        else:
                            max_width = max(
                                len(str(col)) + 2,
                                opportunities_df[col].astype(str).str.len().max() + 2 if len(opportunities_df) > 0 else 15
                            )
                            worksheet.set_column(i, i, min(max_width, 30))

                # Format timestamp columns with date format
                timestamp_format = workbook.add_format({'num_format': 'yyyy-mm-dd hh:mm:ss'})
                if 'Opportunities' in writer.sheets:
                    worksheet = writer.sheets['Opportunities']
                    timestamp_cols = [i for i, col in enumerate(opportunities_df.columns) if 'timestamp' in col.lower()]
                    for row in range(1, len(opportunities_df) + 1):
                        for col in timestamp_cols:
                            # Skip formatting for empty cells
                            if isinstance(opportunities_df.iloc[row-1, col], str) and opportunities_df.iloc[row-1, col]:
                                worksheet.write(row, col, opportunities_df.iloc[row-1, col], timestamp_format)

            output.seek(0)
        except Exception as e:
            print(f"\nError saving workbook: {str(e)}")
            return Response({'error': f'Failed to create Excel file: {str(e)}'},
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Return the Excel file as a response
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        filename = f"{folder_name}_final_report.xlsx"
        response['Content-Disposition'] = f'attachment; filename={filename}'

        return response


class TriggerReportGenerationView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def post(self, request):
        campaign_id = request.data.get('campaign_id')
        template_id = request.data.get('template_id')
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')

        # Check if user is client
        user = request.user
        current_profile = Profile.objects.get(user=user)
        
        # Get campaign
        if not campaign_id:
            return Response({'error': 'campaign_id is required'}, status=400)
            
        campaign = get_object_or_404(Campaign, id=campaign_id)            
                
        if not validate_profile_campaign(current_profile,campaign):
                return Response({'error': 'You do not have permission to access this campaign'},
                            status=status.HTTP_403_FORBIDDEN)        

        excel_file_name = campaign.excel_file_name.split('/')[-1]  # Assuming the structure is 'notices/campaign_id/file_name.xlsx'
        folder_name = excel_file_name.split('.xlsx')[0]

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'{folder_name}_report.xlsx'

        template = Template.objects.get(id=template_id)
        template_name = template.name

        # Prepare Pub/Sub message
        project_id = GCP_PROJECT_ID
        topic_id = PUBSUB_WA_REPORT_TOPIC
        credentials = service_account.Credentials.from_service_account_info(settings.CREDENTIALS_DICT)
        publisher = pubsub_v1.PublisherClient(credentials=credentials)
        topic_path = publisher.topic_path(project_id, topic_id)

        message_data = {
            'folder_name': folder_name,
            'start_date': start_date,
            'end_date': end_date,
            'file_name': filename,
            'campaign_id': campaign_id,
            'template_id': template_id,
            'template_name': template_name,
            'user_email': request.user.email
        }

        message_bytes = json.dumps(message_data).encode('utf-8')
        # Publish message
        future = publisher.publish(topic_path, data=message_bytes)
        try:
            message_id = future.result()
            return Response({
                'success': True,
                'message': 'Report generation queued successfully',
                'message_id': message_id,
                'file_name': filename,
            })
        except Exception as e:
            return Response({'error': str(e)}, status=500)


class WhatsAppTemplates(APIView):
    """API to create and manage WhatsApp templates."""
    permission_classes = [IsAuthenticated]

    def get(self, request, profile_id=None):
        """Get WhatsApp templates, optionally filtered by profile ID."""
        try:
            # Initialize queryset with active templates
            queryset = WhatsAppTemplate.objects.filter(is_active=True)

            # Filter by profile_id if provided
            if profile_id:
                profile = get_object_or_404(Profile, id=profile_id)
                queryset = queryset.filter(profile=profile)

                if not queryset.exists():
                    return Response({"message": "No active WhatsApp templates associated with this profile"},
                                  status=status.HTTP_400_BAD_REQUEST)

            # If no filters applied, return all active templates
            serializer = WhatsAppTemplateSerializer(queryset, many=True)
            return Response(serializer.data)
        except Exception as e:
            db_logger.exception(f"Error in WhatsAppTemplates.get: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """Create a new WhatsApp template."""
        try:
            # Extract profile_id from request data
            profile_id = request.data.get('profile_id')
            profile = None

            # Validate profile_id if provided
            if profile_id:
                try:
                    profile = Profile.objects.get(id=profile_id)
                except Profile.DoesNotExist:
                    return Response({"error": f"Profile with ID {profile_id} not found"},
                                  status=status.HTTP_400_BAD_REQUEST)

            # Create serializer with request data
            serializer_data = request.data.copy()
            if profile:
                serializer_data['profile'] = profile.id

            serializer = WhatsAppTemplateSerializer(data=serializer_data)

            if serializer.is_valid():
                template = serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            db_logger.exception(f"Error in WhatsAppTemplates.post: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, template_id):
        """Update an existing WhatsApp template."""
        try:
            template = get_object_or_404(WhatsAppTemplate, id=template_id, is_active=True)

            # Extract profile_id from request data
            profile_id = request.data.get('profile_id')
            profile = None

            # Validate profile_id if provided
            if profile_id:
                try:
                    profile = Profile.objects.get(id=profile_id)
                except Profile.DoesNotExist:
                    return Response({"error": f"Profile with ID {profile_id} not found"},
                                  status=status.HTTP_400_BAD_REQUEST)

            # Create serializer with request data
            serializer_data = request.data.copy()

            # Handle profile association
            if profile:
                serializer_data['profile'] = profile.id
            elif 'profile' in serializer_data and not serializer_data['profile']:
                # Allow removing profile association by setting to null
                serializer_data['profile'] = None

            serializer = WhatsAppTemplateSerializer(template, data=serializer_data)

            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            db_logger.exception(f"Error in WhatsAppTemplates.put: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, template_id):
        """Soft delete a WhatsApp template by setting is_active to False."""
        try:
            template = get_object_or_404(WhatsAppTemplate, id=template_id, is_active=True)

            # Soft delete by setting is_active to False
            template.is_active = False
            template.save()

            return Response({"message": "Template deleted successfully"}, status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            db_logger.exception(f"Error in WhatsAppTemplates.delete: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EmailTemplates(APIView):
    """API to create and manage Email templates."""
    permission_classes = [IsAuthenticated]

    def get(self, request, profile_id=None):
        """Get Email templates, optionally filtered by profile ID."""
        try:
            # Initialize queryset with active templates
            queryset = EmailTemplate.objects.all()

            # Filter by profile_id if provided
            if profile_id:
                profile = get_object_or_404(Profile, id=profile_id)
                queryset = queryset.filter(profile=profile)

                if not queryset.exists():
                    return Response({"message": "No active Email templates associated with this profile"},
                                  status=status.HTTP_400_BAD_REQUEST)

            # If no filters applied, return all active templates
            serializer = EmailTemplateSerializer(queryset, many=True)
            return Response(serializer.data)
        except Exception as e:
            db_logger.exception(f"Error in EmailTemplates.get: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """Create a new Email template."""
        try:
            # Extract profile_id from request data
            profile_id = request.data.get('profile_id')
            profile = None

            # Validate profile_id if provided
            if profile_id:
                try:
                    profile = Profile.objects.get(id=profile_id)
                except Profile.DoesNotExist:
                    return Response({"error": f"Profile with ID {profile_id} not found"},
                                  status=status.HTTP_400_BAD_REQUEST)

            # Create serializer with request data
            serializer_data = request.data.copy()
            if profile:
                serializer_data['profile'] = profile.id

            serializer = EmailTemplateSerializer(data=serializer_data)

            if serializer.is_valid():
                template = serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            db_logger.exception(f"Error in EmailTemplates.post: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, template_id):
        """Update an existing Email template."""
        try:
            template = get_object_or_404(EmailTemplate, id=template_id, is_active=True)

            # Extract profile_id from request data
            profile_id = request.data.get('profile_id')
            profile = None

            # Validate profile_id if provided
            if profile_id:
                try:
                    profile = Profile.objects.get(id=profile_id)
                except Profile.DoesNotExist:
                    return Response({"error": f"Profile with ID {profile_id} not found"},
                                  status=status.HTTP_400_BAD_REQUEST)

            # Create serializer with request data
            serializer_data = request.data.copy()

            # Handle profile association
            if profile:
                serializer_data['profile'] = profile.id
            elif 'profile' in serializer_data and not serializer_data['profile']:
                # Allow removing profile association by setting to null
                serializer_data['profile'] = None

            serializer = EmailTemplateSerializer(template, data=serializer_data)

            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            db_logger.exception(f"Error in EmailTemplates.put: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, template_id):
        """Soft delete an Email template by setting is_active to False."""
        try:
            template = get_object_or_404(EmailTemplate, id=template_id, is_active=True)

            # Soft delete by setting is_active to False
            template.is_active = False
            template.save()

            return Response({"message": "Template deleted successfully"}, status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            db_logger.exception(f"Error in EmailTemplates.delete: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TemplatesView(APIView):
    """API to create and manage parent templates that link WhatsApp and Email templates."""
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request, campaign_id=None, user_id=None):
        """Get parent templates, optionally filtered by campaign ID, profile ID, or template ID."""
        try:
            # Check if user is client
            user = request.user
            current_profile = Profile.objects.get(user=user)
            current_profile_type = current_profile.profile_type

            template_id = request.query_params.get('template_id')

            # If template_id is provided, return that specific template
            if template_id:
                template = Template.objects.get(id=template_id)
                if not template:
                    return Response({"message": "No active template found with this ID"}, status=status.HTTP_400_BAD_REQUEST)
                serializer = TemplateSerializer(template)
                return Response(serializer.data)

            # Initialize queryset with active templates
            queryset = Template.objects.all()

            if current_profile_type == ProfileType.admin.name:
                pass
            elif current_profile_type == ProfileType.client.name:
                # If user is client, only show templates associated with their profile
                queryset = queryset.filter(profile=current_profile)
            elif current_profile_type == ProfileType.sub_client.name:
                client = Profile.objects.get(id=current_profile.parent_client.id)
                queryset = queryset.filter(profile=client)                         
            

            # Filter by campaign_id if provided
            if campaign_id:
                campaign = Campaign.objects.get(id=campaign_id)
                if not campaign:
                    return Response({"message": "No active campaign found with this ID"}, status=status.HTTP_400_BAD_REQUEST)
                
                if not validate_profile_campaign(current_profile,campaign):
                    return Response({'error': 'You do not have permission to access this campaign'},
                                   status=status.HTTP_403_FORBIDDEN)     

                client_profile = Campaign.objects.get(id=campaign_id).client
                queryset = queryset.filter(profile=client_profile)
                if not queryset.exists():
                    return Response({"message": "No active templates associated with this campaign"},
                                  status=status.HTTP_400_BAD_REQUEST)
            # Filter by profile_id if provided
            if user_id:
                profile = Profile.objects.get(user_id=user_id)
                if not profile:
                    return Response({"message": "No active profile found with this ID"}, status=status.HTTP_400_BAD_REQUEST)
                queryset = queryset.filter(profile=profile)
                if not queryset.exists():
                    return Response({"message": "No active templates associated with this profile"},
                                  status=status.HTTP_400_BAD_REQUEST)
            # If no filters applied, return all active templates
            serializer = TemplateSerializer(queryset, many=True)
            return Response(serializer.data)
        except Exception as e:
            db_logger.exception(f"Error in Templates.get: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """Create a new parent template linking WhatsApp and Email templates."""
        try:
            # Extract data from request
            data = request.data.copy()

            # DRF will handle validation against non-existent objects

            template = Template(
                template_id=data.get('template_id'),
                name=data.get('name'),
                description=data.get('description')
            )

            # Set foreign keys directly if provided
            if 'whatsapp_template_id' in data and data['whatsapp_template_id']:
                whatsapp_template = get_object_or_404(WhatsAppTemplate, id=data['whatsapp_template_id'])
                template.whatsapp_template = whatsapp_template

            if 'email_template_id' in data and data['email_template_id']:
                email_template = get_object_or_404(EmailTemplate, id=data['email_template_id'])
                template.email_template = email_template

            # if 'campaign_id' in data and data['campaign_id']:
            #     campaign = get_object_or_404(Campaign, id=data['campaign_id'])
            #     template.campaign = campaign

            if 'profile_id' in data and data['profile_id']:
                profile = get_object_or_404(Profile, id=data['profile_id'])
                template.profile = profile

            # Save the template
            template.save()

            # Return the serialized template
            serializer = TemplateSerializer(template)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            db_logger.exception(f"Error in Templates.post: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, template_id):
        """Update an existing parent template."""
        try:
            template = get_object_or_404(Template, id=template_id, is_active=True)

            # Extract template IDs, campaign_id, and profile_id from request data
            whatsapp_template_id = request.data.get('whatsapp_template_id')
            email_template_id = request.data.get('email_template_id')
            # campaign_id = request.data.get('campaign_id')
            profile_id = request.data.get('profile_id')

            # Validate template IDs, campaign, and profile if provided
            whatsapp_template = None
            email_template = None
            # campaign = None
            profile = None

            if whatsapp_template_id:
                whatsapp_template = get_object_or_404(WhatsAppTemplate, id=whatsapp_template_id, is_active=True)

            if email_template_id:
                email_template = get_object_or_404(EmailTemplate, id=email_template_id, is_active=True)

            # if campaign_id:
            #     campaign = get_object_or_404(Campaign, id=campaign_id)

            if profile_id:
                profile = get_object_or_404(Profile, id=profile_id)

            # Create serializer with request data
            serializer_data = request.data.copy()

            # Handle template associations
            if whatsapp_template:
                serializer_data['whatsapp_template'] = whatsapp_template.id
            elif 'whatsapp_template' in serializer_data and not serializer_data['whatsapp_template']:
                serializer_data['whatsapp_template'] = None

            if email_template:
                serializer_data['email_template'] = email_template.id
            elif 'email_template' in serializer_data and not serializer_data['email_template']:
                serializer_data['email_template'] = None

            # Handle campaign association
            # if campaign:
            #     serializer_data['campaign'] = campaign.id
            # elif 'campaign' in serializer_data and not serializer_data['campaign']:
            #     serializer_data['campaign'] = None

            # Handle profile association
            if profile:
                serializer_data['profile'] = profile.id
            elif 'profile' in serializer_data and not serializer_data['profile']:
                serializer_data['profile'] = None

            serializer = TemplateSerializer(template, data=serializer_data)

            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            db_logger.exception(f"Error in Templates.put: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, template_id):
        """Soft delete a parent template by setting is_active to False."""
        try:
            template = get_object_or_404(Template, id=template_id, is_active=True)

            # Soft delete by setting is_active to False
            template.is_active = False
            template.save()

            return Response({"message": "Template deleted successfully"}, status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            db_logger.exception(f"Error in Templates.delete: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TriggerWhatsAppSendMessageView(APIView):
    """API to trigger WhatsApp message sending for a campaign using Pub/Sub."""
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def post(self, request):
        BATCH_SIZE = getattr(settings, 'WHATSAPP_BATCH_SIZE', 50)
        PUBSUB_PUBLISH_BATCH_SIZE = 10
        """Process campaign data and distribute WhatsApp message sending tasks via Pub/Sub."""
        try:
            # Get campaign_id and template_id from request
            campaign_id = request.data.get('campaign_id')
            template_id = request.data.get('template_id')
            has_co_borrowers = request.data.get('has_co_borrowers', False)
            file_upload = request.data.get('file_upload', False)
            folder_name = request.data.get('folder_name')
            notice_id = request.data.get('notice_id')  # ADD THIS LINE

            if not campaign_id:
                return Response({'error': 'campaign_id is required'}, status=status.HTTP_400_BAD_REQUEST)
            if not template_id:
                return Response({'error': 'template_id is required'}, status=status.HTTP_400_BAD_REQUEST)

            # Optional: Add notice_id validation if required
            if not notice_id:
                # You can either make it required or just log a warning
                db_logger.warning(f"No notice_id provided for campaign {campaign_id}")
                # Uncomment the line below if notice_id should be required:
                # return Response({'error': 'notice_id is required'}, status=status.HTTP_400_BAD_REQUEST)

            # Get campaign details
            campaign = Campaign.objects.get(id=campaign_id)
            if not campaign:
                return Response({'error': 'Invalid campaign_id'}, status=status.HTTP_400_BAD_REQUEST)

            template = Template.objects.get(id=template_id)
            if not template:
                return Response({'error': 'Invalid template_id'}, status=status.HTTP_400_BAD_REQUEST)
            
            user=request.user
            current_profile = Profile.objects.get(user=user)
            if not validate_profile_campaign(current_profile,campaign):
                    return Response({'error': 'You do not have permission to access this campaign'},
                                status=status.HTTP_403_FORBIDDEN)          
                    
            # check if campaign with template there in campaign template table, if it is there then continue else insert it
            campaign_template = CampaignTemplate.objects.filter(campaign=campaign, template=template).first()
            if not campaign_template:
                campaign_template = CampaignTemplate.objects.create(
                    campaign=campaign,
                    template=template
                )
                campaign_template.save()

            client_name = User.objects.filter(id=template.profile.user_id).first().first_name

            closed_disputes = Dispute.objects.filter(status='closed',campaign_id=campaign.id).values_list('loan_id',flat=True)            
            print(f'no. of closed disputes is {len(closed_disputes)}')

            # Check flow type from campaign
            flow_type = campaign.flow_type
            whatsapp_template = template.whatsapp_template

            # Handle different flows
            if flow_type == 'conciliation':
                # Get template mapping from template model
                template_parameter_mapping = getattr(whatsapp_template, 'parameter_mapping', {})

                # For conciliation flow, we might not always need a notice
                if not template.whatsapp_template:
                    return Response({'error': 'No WhatsApp template associated with this template'}, 
                                   status=status.HTTP_400_BAD_REQUEST)

            # Check if excel file exists
            if not campaign.excel_file_name or not campaign.excel_file_uploaded:
                return Response({'error': 'Campaign does not have an Excel file'},
                               status=status.HTTP_400_BAD_REQUEST)

            # Read Excel file from GCS
            try:
                excel_data = gcs_manager.read_file(campaign.excel_file_name)
                excel_file = io.BytesIO(excel_data)

                # Read the first sheet (main data)
                df_original = pd.read_excel(excel_file, sheet_name=0)  # Sheet 1 (index 0)

                #Notices must not be sent for closed disputes. Remove closed disputes from df
                df = df_original[~df_original['Loan ID'].isin(closed_disputes)]    

                #if all disputes are closed, return gracfully with appropriate message  
                if len(df) == 0:
                    return Response(data='All disputes of the campaign are closed. So not sending out notices',status=status.HTTP_200_OK)
                
                # Read Sheet 2 automatically if flow_type is 'conciliation'
                sheet2_data = {}
                sheet2_df = None
                if flow_type == 'conciliation':
                    try:
                        excel_file.seek(0)  # Reset file pointer
                        sheet2_df = pd.read_excel(excel_file, sheet_name=1)  # Sheet 2 (index 1)
                        
                        # Check if Sheet 2 has data
                        if len(sheet2_df) > 0:
                            # Your Sheet 2 format has column headers with data in the first row
                            # Convert the first row to dictionary with column names as keys
                            sheet2_data = sheet2_df.iloc[0].to_dict()

                            # Clean up any NaN values and convert to string
                            if sheet2_data:
                                sheet2_data = {str(k): (str(v) if pd.notna(v) else '') for k, v in sheet2_data.items()}

                            # Log available variables for debugging
                            db_logger.info(f"Successfully read Sheet 2 for conciliation flow. Variables available: {list(sheet2_data.keys()) if sheet2_data else 'No data'}")
                            db_logger.info(f"Sheet 2 data: {sheet2_data}")
                        else:
                            db_logger.warning("Sheet 2 exists but contains no data rows")
                            sheet2_data = {}

                    except Exception as sheet2_error:
                        db_logger.warning(f"Failed to read Sheet 2 for conciliation flow: {str(sheet2_error)}")
                        # Set default hardcoded values if Sheet 2 is not available
                        sheet2_data = {
                            'client_name': client_name,
                            'date': '',
                            'start_time': '',
                            'end_time': '',
                            'link': ''
                        }
                        db_logger.info(f"Using hardcoded default Sheet 2 data: {sheet2_data}")

            except Exception as e:
                return Response({'error': f'Failed to read Excel file: {str(e)}'},
                               status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Check required columns based on flow type
            if flow_type == 'conciliation':
                # Define required columns for conciliation flow (only check Sheet 1 columns)
                base_required_columns = ['Borrower\'s Number', 'Loan ID']

                # Additional columns based on template type (only check what should be in Sheet 1)
                if getattr(whatsapp_template, 'is_termination_notice', False):
                    required_columns = base_required_columns + [
                        'Borrower\'s Email', 'Name of the Borrower', 'Notice Date', 
                        'Notice Amount', 'Payment Link'
                    ]
                elif getattr(whatsapp_template, 'is_s138_notice', False):
                    required_columns = base_required_columns + [
                        'Borrower\'s Email', 'Name of the Borrower', 'Notice Date', 
                        'Notice Amount', 'Payment Link', 'Dishonour Date'
                    ]
                elif getattr(whatsapp_template, 'is_payment_request_notice', False):
                    required_columns = base_required_columns + [
                        'Borrower\'s Email', 'Name of the Borrower', 'Notice Date', 
                        'Notice Amount', 'Payment Link'
                    ]
                elif getattr(whatsapp_template, 'conciliation_notice_1', False):
                    # For conciliation notices, we only need basic loan info from Sheet 1
                    # The meeting details come from Sheet 2
                    required_columns = base_required_columns
                elif getattr(whatsapp_template, 'conciliation_notice_2', False):
                    required_columns = base_required_columns
                elif getattr(whatsapp_template, 'conciliation_notice_3', False):
                    required_columns = base_required_columns
                elif getattr(whatsapp_template, 'conciliation_notice_4', False):
                    required_columns = base_required_columns
                else:
                    # Generic conciliation template - only check Sheet 1 columns
                    # Filter out Sheet 2 columns from parameter mapping
                    sheet2_columns = ['client_name', 'date', 'start_time', 'end_time', 'link']
                    additional_columns = []
                    if template_parameter_mapping:
                        for column_name in template_parameter_mapping.values():
                            if column_name not in sheet2_columns:
                                additional_columns.append(column_name)
                    required_columns = base_required_columns + additional_columns
            else:
                # Original arbitration flow columns
                required_columns = ['Borrower\'s Number', 'Loan ID']

            # Only check for missing columns in Sheet 1 (main data)
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return Response({'error': f'Excel file missing required columns in Sheet 1: {missing_columns}'},
                               status=status.HTTP_400_BAD_REQUEST)

            # For conciliation flow, validate that Sheet 2 has the required data
            if flow_type == 'conciliation':
                sheet2_required_fields = []
                if getattr(whatsapp_template, 'conciliation_notice_1', False):
                    sheet2_required_fields = ['date', 'start_time', 'end_time', 'link']
                elif getattr(whatsapp_template, 'conciliation_notice_2', False):
                    sheet2_required_fields = ['date', 'start_time', 'end_time', 'link']
                elif getattr(whatsapp_template, 'conciliation_notice_3', False):
                    sheet2_required_fields = ['date', 'start_time', 'end_time', 'link']
                elif getattr(whatsapp_template, 'conciliation_notice_4', False):
                    sheet2_required_fields = ['link']

                # Check if required Sheet 2 fields are present and not empty
                missing_sheet2_fields = []
                for field in sheet2_required_fields:
                    if field not in sheet2_data or not sheet2_data.get(field, '').strip():
                        missing_sheet2_fields.append(field)

                if missing_sheet2_fields:
                    db_logger.warning(f"Missing or empty Sheet 2 fields: {missing_sheet2_fields}")
                    # You can either return an error or set default values
                    # For now, let's set empty defaults and log a warning
                    for field in missing_sheet2_fields:
                        sheet2_data[field] = ''
                    db_logger.info(f"Set default empty values for missing Sheet 2 fields: {missing_sheet2_fields}")

            # Ensure client_name is always available in sheet2_data for conciliation
            if flow_type == 'conciliation':
                if 'client_name' not in sheet2_data or not sheet2_data.get('client_name', '').strip():
                    sheet2_data['client_name'] = client_name
                    db_logger.info(f"Set client_name in Sheet 2 data: {client_name}")

            # Get WhatsApp template by ID
            try:
                template = Template.objects.get(id=template_id)
                if not template:
                    return Response({'error': 'No active template found with the specified ID'}, 
                                   status=status.HTTP_400_BAD_REQUEST)
                whatsapp_template = template.whatsapp_template

                if whatsapp_template is None:
                    return Response({'error': 'No WhatsApp template associated with the specified template'}, 
                                   status=status.HTTP_400_BAD_REQUEST)
                wa_template_id = whatsapp_template.id
                whatsapp_template_id = whatsapp_template.whatsapp_template_id
                lang_code = whatsapp_template.lang_code
                attachment_required = whatsapp_template.requires_attachment
            except WhatsAppTemplate.DoesNotExist:
                return Response({'error': 'No active WhatsApp template found with the specified ID'},
                               status=status.HTTP_400_BAD_REQUEST)

            # Split the dataframe into batches
            total_rows = len(df)
            batch_count = math.ceil(total_rows / BATCH_SIZE)

            # Update WhatsApp template status
            whatsapp_template.whatsapp_status = 'processing'
            whatsapp_template.save()

            # Set up Pub/Sub client
            credentials = service_account.Credentials.from_service_account_info(CREDENTIALS_DICT)
            publisher = pubsub_v1.PublisherClient(credentials=credentials)
            topic_path = publisher.topic_path(GCP_PROJECT_ID, PUBSUB_WHATSAPP_MESSAGES_TOPIC)

            # Split batches into publishing groups to avoid overwhelming Pub/Sub
            publish_futures = []
            published_batches = 0

            # Process each batch
            for batch_num in range(batch_count):
                start_idx = batch_num * BATCH_SIZE
                end_idx = min(start_idx + BATCH_SIZE, total_rows)

                # Extract batch data
                batch_df = df.iloc[start_idx:end_idx]

                # Create batch data payload
                batch_data = {
                    'campaign_id': campaign_id,
                    'notice_id': notice_id,  # ADD THIS LINE
                    'root_template_id': template_id,
                    'template_name': template.name,
                    'template_id': wa_template_id,
                    'whatsapp_template_id': whatsapp_template_id,
                    'lang_code': lang_code,
                    'attachment_required': attachment_required,
                    'excel_file_path': campaign.excel_file_name,
                    'pdf_file_path': template.pdf_file_name,
                    'user_id': request.user.id,
                    'batch_number': batch_num,
                    'total_batches': batch_count,
                    'is_s21_notice': whatsapp_template.is_s21_notice,
                    'has_co_borrowers': has_co_borrowers,
                    'flow_type': flow_type,
                    'client_name': client_name,
                    'has_body_params': whatsapp_template.has_body_params,
                    'file_upload': file_upload,
                    'folder_name': folder_name,
                    'df_json': batch_df.to_json(orient='records')
                }

                # Add conciliation-specific flags if it's conciliation flow
                if flow_type == 'conciliation':
                    batch_data.update({
                        'is_termination_notice': getattr(whatsapp_template, 'is_termination_notice', False),
                        'is_s138_notice': getattr(whatsapp_template, 'is_s138_notice', False),
                        'is_payment_request_notice': getattr(whatsapp_template, 'is_payment_request_notice', False),
                        'is_demand_notice': getattr(whatsapp_template, 'is_demand_notice', False),
                        'conciliation_notice_1': getattr(whatsapp_template, 'conciliation_notice_1', False),
                        'conciliation_notice_2': getattr(whatsapp_template, 'conciliation_notice_2', False),
                        'conciliation_notice_3': getattr(whatsapp_template, 'conciliation_notice_3', False),
                        'conciliation_notice_4': getattr(whatsapp_template, 'conciliation_notice_4', False),
                        'template_parameter_mapping': template_parameter_mapping,
                        'sheet2_data': sheet2_data
                    })

                # Encode and publish message
                data_bytes = json.dumps(batch_data).encode('utf-8')

                # Include attributes for tracing and ordering if needed
                attributes = {
                    'campaign_id': str(campaign_id),
                    'notice_id': str(notice_id) if notice_id else 'none',  # ADD THIS LINE
                    'batch_number': str(batch_num),
                    'total_batches': str(batch_count),
                    'template_id': str(wa_template_id),
                    'flow_type': flow_type,
                }

                try:
                    future = publisher.publish(
                        topic_path,
                        data=data_bytes,
                        **attributes
                    )
                    publish_futures.append(future)
                    published_batches += 1

                    # Log progress periodically
                    if batch_num % 10 == 0:
                        db_logger.info(f"Published {batch_num+1}/{batch_count} batches for campaign {campaign_id}")

                    # Wait for batches to be published every PUBSUB_PUBLISH_BATCH_SIZE to avoid overwhelming the API
                    if len(publish_futures) >= PUBSUB_PUBLISH_BATCH_SIZE:
                        # Wait for all futures in the current batch
                        for future in publish_futures:
                            try:
                                future.result(timeout=30)  # 30 second timeout per message
                            except Exception as e:
                                db_logger.error(f"Error publishing to Pub/Sub: {str(e)}")

                        # Clear the futures list after waiting
                        publish_futures = []

                        # Add a small delay to smooth out the publishing rate
                        time.sleep(0.1)

                except Exception as e:
                    db_logger.error(f"Failed to publish batch {batch_num} to Pub/Sub: {str(e)}")
                    # Continue with other batches even if one fails

            # Wait for any remaining publish operations to complete
            if publish_futures:
                for future in publish_futures:
                    try:
                        future.result(timeout=30)
                    except Exception as e:
                        db_logger.error(f"Error publishing to Pub/Sub: {str(e)}")

            db_logger.info(f"Successfully published {published_batches}/{batch_count} batches to Pub/Sub for campaign {campaign_id}, notice {notice_id}")

            # Log final Sheet 2 data being sent
            if flow_type == 'conciliation':
                db_logger.info(f"Final Sheet 2 data being sent with batches: {sheet2_data}")

            # Return response
            return Response({
                'message': 'WhatsApp campaign distribution initiated via Pub/Sub',
                'campaign_id': campaign_id,
                'notice_id': notice_id,  # ADD THIS LINE
                'flow_type': flow_type,
                'client_name': client_name,
                'total_messages': total_rows,
                'batch_count': batch_count,
                'processed_batches': published_batches,
                'has_co_borrowers': has_co_borrowers,
                'sheet2_data': sheet2_data if flow_type == 'conciliation' else None
            }, status=status.HTTP_202_ACCEPTED)

        except Exception as e:
            db_logger.exception(f"Error in TriggerWhatsAppSendMessageView: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TriggerEmailSendMessageView(APIView):
    """API to trigger Email message sending for a campaign using Pub/Sub and batching."""
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def post(self, request):
        BATCH_SIZE = getattr(settings, 'EMAIL_BATCH_SIZE', 50)
        PUBSUB_PUBLISH_BATCH_SIZE = 10
        """Process campaign data and distribute Email message sending tasks via Pub/Sub."""
        try:
            # Get campaign_id and template_id from request
            campaign_id = request.data.get('campaign_id')
            template_id = request.data.get('template_id')
            has_co_borrowers = request.data.get('has_co_borrowers', False)
            file_upload = request.data.get('file_upload', False)
            folder_name = request.data.get('folder_name')

            if not campaign_id:
                return Response({'error': 'campaign_id is required'}, status=status.HTTP_400_BAD_REQUEST)
            if not template_id:
                return Response({'error': 'template_id is required'}, status=status.HTTP_400_BAD_REQUEST)

            # Get campaign details
            campaign = Campaign.objects.get(id=campaign_id)
            if not campaign:
                return Response({'error': 'Invalid campaign_id'}, status=status.HTTP_400_BAD_REQUEST)

            template = Template.objects.get(id=template_id)
            if not template:
                return Response({'error': 'Invalid template_id'}, status=status.HTTP_400_BAD_REQUEST)
            
            user=request.user
            current_profile = Profile.objects.get(user=user)            

            # Check permissions based on user's profile type
            if not validate_profile_campaign(current_profile,campaign):
                    return Response({'error': 'You do not have permission to access this campaign'},
                                status=status.HTTP_403_FORBIDDEN)            

            # check if campaign with template there in campaign template table, if it is there then continue else insert it
            campaign_template = CampaignTemplate.objects.filter(campaign=campaign, template=template).first()
            if not campaign_template:
                campaign_template = CampaignTemplate.objects.create(
                    campaign=campaign,
                    template=template
                )
                campaign_template.save()

            client_name = User.objects.filter(id=template.profile.user_id).first().first_name

            closed_disputes = Dispute.objects.filter(status='closed',campaign_id=campaign.id).values_list('loan_id',flat=True)            
            print(f'no. of closed disputes is {len(closed_disputes)}')

            # Check flow type from campaign
            flow_type = campaign.flow_type
            email_template = template.email_template

            # Handle different flows
            if flow_type == 'conciliation':
                # Get template mapping from template model
                template_parameter_mapping = getattr(email_template, 'parameter_mapping', {})

                # For conciliation flow, we might not always need a notice
                if not template.email_template:
                    return Response({'error': 'No Email template associated with this template'}, 
                                   status=status.HTTP_400_BAD_REQUEST)

            # Check if excel file exists
            if not campaign.excel_file_name or not campaign.excel_file_uploaded:
                return Response({'error': 'Campaign does not have an Excel file'},
                               status=status.HTTP_400_BAD_REQUEST)

            # Read Excel file from GCS
            try:
                excel_data = gcs_manager.read_file(campaign.excel_file_name)
                excel_file = io.BytesIO(excel_data)

                # Read the first sheet (main data)
                df_original = pd.read_excel(excel_file, sheet_name=0)  # Sheet 1 (index 0)                

                #Notices must not be sent for closed disputes. Remove closed disputes from df
                df = df_original[~df_original['Loan ID'].isin(closed_disputes)]    

                #if all disputes are closed, return gracfully with appropriate message  
                if len(df) == 0:
                    return Response(data='All disputes of the campaign are closed. So not sending out notices',status=status.HTTP_200_OK)

                # Read Sheet 2 automatically if flow_type is 'conciliation'
                sheet2_data = {}
                sheet2_df = None
                if flow_type == 'conciliation':
                    try:
                        excel_file.seek(0)  # Reset file pointer
                        sheet2_df = pd.read_excel(excel_file, sheet_name=1)  # Sheet 2 (index 1)

                        # Check if Sheet 2 has data
                        if len(sheet2_df) > 0:
                            # Your Sheet 2 format has column headers with data in the first row
                            # Convert the first row to dictionary with column names as keys
                            sheet2_data = sheet2_df.iloc[0].to_dict()

                            # Clean up any NaN values and convert to string
                            if sheet2_data:
                                sheet2_data = {str(k): (str(v) if pd.notna(v) else '') for k, v in sheet2_data.items()}

                            # Log available variables for debugging
                            db_logger.info(f"Successfully read Sheet 2 for conciliation flow. Variables available: {list(sheet2_data.keys()) if sheet2_data else 'No data'}")
                            db_logger.info(f"Sheet 2 data: {sheet2_data}")
                        else:
                            db_logger.warning("Sheet 2 exists but contains no data rows")
                            sheet2_data = {}

                    except Exception as sheet2_error:
                        db_logger.warning(f"Failed to read Sheet 2 for conciliation flow: {str(sheet2_error)}")
                        # Set default hardcoded values if Sheet 2 is not available
                        sheet2_data = {
                            'client_name': client_name,
                            'date': '',
                            'start_time': '',
                            'end_time': '',
                            'link': ''
                        }
                        db_logger.info(f"Using hardcoded default Sheet 2 data: {sheet2_data}")

            except Exception as e:
                return Response({'error': f'Failed to read Excel file: {str(e)}'},
                               status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Check required columns based on flow type
            if flow_type == 'conciliation':
                # Define required columns for conciliation flow
                base_required_columns = ['Borrower\'s Email', 'Loan ID']

                # Additional columns based on template type
                if getattr(email_template, 'is_termination_notice', False):
                    required_columns = base_required_columns + [
                        'Name of the Borrower', 'Notice Date', 
                        'Notice Amount', 'Payment Link'
                    ]
                elif getattr(email_template, 'is_s138_notice', False):
                    required_columns = base_required_columns + [
                        'Borrower\'s Email', 'Name of the Borrower', 'Notice Date', 
                        'Notice Amount', 'Payment Link', 'Dishonour Date'
                    ]
                elif getattr(email_template, 'is_payment_request_notice', False):
                    required_columns = base_required_columns + [
                        'Borrower\'s Email', 'Name of the Borrower', 'Notice Date', 
                        'Notice Amount', 'Payment Link'
                    ]
                elif getattr(email_template, 'conciliation_notice_1', False):
                    required_columns = base_required_columns
                elif getattr(email_template, 'conciliation_notice_4', False):
                    required_columns = base_required_columns
                elif getattr(email_template, 'conciliation_email_reminder', False):
                    required_columns = base_required_columns
                elif getattr(email_template, 'is_payment_reminder', False):
                    required_columns = base_required_columns + [
                        'borrower_name', 'reminder_amount', 'reminder_date'
                    ]
                else:
                    # Generic conciliation template - only check Sheet 1 columns
                    # Filter out Sheet 2 columns from parameter mapping
                    sheet2_columns = ['client_name', 'date', 'start_time', 'end_time', 'link']
                    additional_columns = []
                    if template_parameter_mapping:
                        for column_name in template_parameter_mapping.values():
                            if column_name not in sheet2_columns:
                                additional_columns.append(column_name)
                    required_columns = base_required_columns + additional_columns
            else:
                # Original arbitration flow columns
                required_columns = ['Borrower\'s Email', 'Loan ID']

            # Add co-borrower columns to required columns if has_co_borrowers is True
            if has_co_borrowers:
                # Check for co-borrower email columns (not necessarily required, but we should validate if they exist)
                co_borrower_columns_found = []
                for i in range(1, 5):  # Up to 4 co-borrowers
                    email_col = f"Co-borrower's Email{i}"
                    name_col = f"Name of the Co-borrower{i}"

                    if email_col in df.columns:
                        co_borrower_columns_found.append(email_col)
                        # If co-borrower email exists, the name column should ideally exist too
                        if name_col in df.columns:
                            co_borrower_columns_found.append(name_col)

                db_logger.info(f"Found {len(co_borrower_columns_found)} co-borrower related columns in Excel file")

                # If has_co_borrowers is True but no co-borrower columns found, log a warning
                if not co_borrower_columns_found:
                    db_logger.warning("has_co_borrowers is True but no co-borrower email columns found in Excel file")

            # Only check for missing columns in Sheet 1 (main data)
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return Response({'error': f'Excel file missing required columns in Sheet 1: {missing_columns}'},
                               status=status.HTTP_400_BAD_REQUEST)

            # For conciliation flow, validate that Sheet 2 has the required data
            if flow_type == 'conciliation':
                sheet2_required_fields = []
                if getattr(email_template, 'conciliation_notice_1', False):
                    sheet2_required_fields = ['date', 'start_time', 'end_time', 'case_manager_name', 'case_manager_email','link']
                elif getattr(email_template, 'conciliation_notice_4', False):
                    sheet2_required_fields = ['date','link']
                elif getattr(email_template, 'conciliation_email_reminder', False):
                    sheet2_required_fields = ['date', 'start_time', 'end_time', 'link']

                # Check if required Sheet 2 fields are present and not empty
                missing_sheet2_fields = []
                for field in sheet2_required_fields:
                    if field not in sheet2_data or not sheet2_data.get(field, '').strip():
                        missing_sheet2_fields.append(field)

                if missing_sheet2_fields:
                    db_logger.warning(f"Missing or empty Sheet 2 fields: {missing_sheet2_fields}")
                    # You can either return an error or set default values
                    # For now, let's set empty defaults and log a warning
                    for field in missing_sheet2_fields:
                        sheet2_data[field] = ''
                    db_logger.info(f"Set default empty values for missing Sheet 2 fields: {missing_sheet2_fields}")

            # Ensure client_name is always available in sheet2_data for conciliation
            if flow_type == 'conciliation':
                if 'client_name' not in sheet2_data or not sheet2_data.get('client_name', '').strip():
                    sheet2_data['client_name'] = client_name
                    db_logger.info(f"Set client_name in Sheet 2 data: {client_name}")

            # Get Email template by ID
            try:
                template = Template.objects.get(id=template_id)
                if not template:
                    return Response({'error': 'No active template found with the specified ID'}, 
                                   status=status.HTTP_400_BAD_REQUEST)
                email_template = template.email_template

                if email_template is None:
                    return Response({'error': 'No Email template associated with the specified template'}, 
                                   status=status.HTTP_400_BAD_REQUEST)
                e_template_id = email_template.id
                email_template_id = email_template.email_template_id
                attachment_required = email_template.requires_attachment
            except EmailTemplate.DoesNotExist:
                return Response({'error': 'No active Email template found with the specified ID'},
                               status=status.HTTP_400_BAD_REQUEST)

            # Calculate expected total emails including co-borrowers
            total_rows = len(df)
            estimated_total_emails = total_rows  # At least one email per row (primary borrower)

            if has_co_borrowers:
                # Count additional co-borrower emails
                co_borrower_count = 0
                for i in range(1, 5):
                    email_col = f"Co-borrower's Email{i}"
                    if email_col in df.columns:
                        # Count non-empty co-borrower emails
                        co_borrower_count += df[email_col].notna().sum()

                estimated_total_emails += co_borrower_count
                db_logger.info(f"Estimated total emails: {estimated_total_emails} ({total_rows} primary + {co_borrower_count} co-borrowers)")

            # Split the dataframe into batches
            batch_count = math.ceil(total_rows / BATCH_SIZE)

            # Update Email template status
            email_template.email_status = 'processing'
            email_template.save()

            # Set up Pub/Sub client
            credentials = service_account.Credentials.from_service_account_info(CREDENTIALS_DICT)
            publisher = pubsub_v1.PublisherClient(credentials=credentials)
            topic_path = publisher.topic_path(GCP_PROJECT_ID, PUBSUB_EMAILS_TOPIC)

            # Split batches into publishing groups to avoid overwhelming Pub/Sub
            publish_futures = []
            published_batches = 0

            # Process each batch
            for batch_num in range(batch_count):
                start_idx = batch_num * BATCH_SIZE
                end_idx = min(start_idx + BATCH_SIZE, total_rows)

                # Extract batch data
                batch_df = df.iloc[start_idx:end_idx]

                # Create batch data payload
                batch_data = {
                    'campaign_id': campaign_id,
                    'root_template_id': template_id,
                    'template_name': template.name,
                    'template_id': e_template_id,
                    'email_template_id': email_template_id,
                    'attachment_required': attachment_required,
                    'excel_file_path': campaign.excel_file_name,
                    'pdf_file_path': template.pdf_file_name,
                    'user_id': request.user.id,
                    'batch_number': batch_num,
                    'total_batches': batch_count,
                    'is_s21_notice': email_template.is_s21_notice,
                    'has_co_borrowers': has_co_borrowers,
                    'flow_type': flow_type,
                    'client_name': client_name,
                    'file_upload': file_upload,
                    'folder_name': folder_name,
                    'df_json': batch_df.to_json(orient='records')
                }

                # Add conciliation-specific flags if it's conciliation flow
                if flow_type == 'conciliation':
                    batch_data.update({
                        'is_termination_notice': getattr(email_template, 'is_termination_notice', False),
                        'is_s138_notice': getattr(email_template, 'is_s138_notice', False),
                        'is_payment_request_notice': getattr(email_template, 'is_payment_request_notice', False),
                        'conciliation_notice_1': getattr(email_template, 'conciliation_notice_1', False),
                        'conciliation_notice_4': getattr(email_template, 'conciliation_notice_4', False),
                        'conciliation_email_reminder': getattr(email_template, 'conciliation_email_reminder', False),
                        'is_payment_reminder': getattr(email_template, 'is_payment_reminder', False),
                        'template_parameter_mapping': template_parameter_mapping,
                        'sheet2_data': sheet2_data
                    })

                # Encode and publish message
                data_bytes = json.dumps(batch_data).encode('utf-8')

                # Include attributes for tracing and ordering if needed
                attributes = {
                    'campaign_id': str(campaign_id),
                    'batch_number': str(batch_num),
                    'total_batches': str(batch_count),
                    'template_id': str(e_template_id),
                    'flow_type': flow_type,
                    'has_co_borrowers': str(has_co_borrowers)
                }

                try:
                    # Publish message
                    future = publisher.publish(
                        topic_path,
                        data=data_bytes,
                        **attributes
                    )
                    publish_futures.append(future)
                    published_batches += 1

                    # Log progress periodically
                    if batch_num % 10 == 0:
                        db_logger.info(f"Published {batch_num+1}/{batch_count} batches for campaign {campaign_id}")

                    # Wait for batches to be published every PUBSUB_PUBLISH_BATCH_SIZE to avoid overwhelming the API
                    if len(publish_futures) >= PUBSUB_PUBLISH_BATCH_SIZE:
                        # Wait for all futures in the current batch
                        for future in publish_futures:
                            try:
                                future.result(timeout=30)  # 30 second timeout per message
                            except Exception as e:
                                db_logger.error(f"Error publishing to Pub/Sub: {str(e)}")

                        # Clear the futures list after waiting
                        publish_futures = []

                        # Add a small delay to smooth out the publishing rate
                        time.sleep(0.1)

                except Exception as e:
                    db_logger.error(f"Failed to publish batch {batch_num} to Pub/Sub: {str(e)}")
                    # Continue with other batches even if one fails

            # Wait for any remaining publish operations to complete
            if publish_futures:
                for future in publish_futures:
                    try:
                        future.result(timeout=30)
                    except Exception as e:
                        db_logger.error(f"Error publishing to Pub/Sub: {str(e)}")

            db_logger.info(f"Successfully published {published_batches}/{batch_count} batches to Pub/Sub for campaign {campaign_id}")

            # Log final Sheet 2 data being sent
            if flow_type == 'conciliation':
                db_logger.info(f"Final Sheet 2 data being sent with batches: {sheet2_data}")

            # Return response with updated information
            return Response({
                'message': 'Email campaign distribution initiated via Pub/Sub',
                'campaign_id': campaign_id,
                'flow_type': flow_type,
                'client_name': client_name,
                'total_rows': total_rows,
                'estimated_total_emails': estimated_total_emails,
                'batch_count': batch_count,
                'processed_batches': published_batches,
                'has_co_borrowers': has_co_borrowers,
                'sheet2_data': sheet2_data if flow_type == 'conciliation' else None
            }, status=status.HTTP_202_ACCEPTED)

        except Exception as e:
            db_logger.exception(f"Error in TriggerEmailSendMessageView: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class DashboardBulkActionsView(APIView):
    """API to get dashboard data for Bulk Actions and Sheet uploads."""
    permission_classes = [IsAuthenticated]

    def get(self, request, campaign_id=None):
        """Get dashboard data for Bulk Actions and Sheet uploads.

        If campaign_id is provided, return detailed data for that campaign.
        Otherwise, return summary data for all campaigns.
        """
        try:
            # If campaign_id is provided, return detailed data for that campaign
            if campaign_id:
                return self.get_campaign_detail(request, campaign_id)

            # Get filter parameters
            days = int(request.query_params.get('days', 30))  # Default to last 30 days
            user_id = request.query_params.get('client_id')  # Optional profile filter
            count = request.query_params.get('count')  # Optional count parameter

            # Pagination parameters
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 25))

            # Calculate date range
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)

            # Check user's profile type
            user = request.user
            is_admin = Profile.objects.filter(user=user, profile_type=ProfileType.admin.name).exists()
            is_client = Profile.objects.filter(user=user, profile_type=ProfileType.client.name).exists()
            is_sub_client = Profile.objects.filter(user=user, profile_type=ProfileType.sub_client.name).exists()
            is_case_manager = Profile.objects.filter(user=user, profile_type=ProfileType.case_manager.name).exists()
            is_arbitrator = Profile.objects.filter(user=user, profile_type=ProfileType.arbitrator.name).exists()

            # Base queryset for campaigns
            campaigns_queryset = Campaign.objects.filter(created_at__gte=start_date)

            # Filter based on user's profile type
            if is_admin:
                # Admin can see all campaigns
                # If client_id is provided, filter by that client
                if user_id:
                    try:
                        # If not found, try to find a profile with this user_id
                        profile = Profile.objects.get(user_id=user_id)
                        campaigns_queryset = campaigns_queryset.filter(client=profile)
                    except Profile.DoesNotExist:
                        # If still not found, return empty result
                        return Response({'error': 'Profile not found for the given client_id'}, 
                                        status=status.HTTP_400_BAD_REQUEST)
            elif is_client or is_sub_client:
                if user_id:
                    # If user_id is provided, filter by that client
                    try:
                        profile = Profile.objects.get(user_id=user_id, profile_type__in=[ProfileType.client.name, ProfileType.sub_client.name])
                        campaigns_queryset = campaigns_queryset.filter(client=profile)
                    except Profile.DoesNotExist:
                        return Response({'error': 'Client is not associated with the user'}, 
                                        status=status.HTTP_400_BAD_REQUEST)
                # Client or sub-client can only see their own campaigns
                else:
                    client_profile = Profile.objects.get(user=user, profile_type__in=[ProfileType.client.name, ProfileType.sub_client.name])
                    campaigns_queryset = campaigns_queryset.filter(client=client_profile)
            elif is_case_manager:
                if user_id:
                    # If user_id is provided, filter by that client
                    try:
                        profile = Profile.objects.get(user_id=user_id, profile_type=ProfileType.case_manager.name)
                        case_manager_disputes = Dispute.objects.filter(case_manager_rv=case_manager_profile)
                        # Get campaigns associated with these disputes
                        campaign_ids = case_manager_disputes.values_list('campaign_id', flat=True).distinct()
                        campaigns_queryset = campaigns_queryset.filter(id__in=campaign_ids)
                    except Profile.DoesNotExist:
                        return Response({'error': 'Client is not associated with the case manager'}, 
                                        status=status.HTTP_400_BAD_REQUEST)
                else:
                    # Case manager can only see campaigns for disputes they're assigned to
                    case_manager_profile = Profile.objects.get(user=user, profile_type=ProfileType.case_manager.name)
                    # Get all disputes assigned to this case manager
                    case_manager_disputes = Dispute.objects.filter(case_manager_rv=case_manager_profile)
                    # Get campaigns associated with these disputes
                    campaign_ids = case_manager_disputes.values_list('campaign_id', flat=True).distinct()
                    campaigns_queryset = campaigns_queryset.filter(id__in=campaign_ids)
            elif is_arbitrator:
                if user_id:
                    # If user_id is provided, filter by that client
                    try:
                        profile = Profile.objects.get(user_id=user_id, profile_type=ProfileType.arbitrator.name)
                        arbitrator_disputes = Dispute.objects.filter(arbitrator_rv=arbitrator_profile)
                        # Get campaigns associated with these disputes
                        campaign_ids = arbitrator_disputes.values_list('campaign_id', flat=True).distinct()
                        campaigns_queryset = campaigns_queryset.filter(id__in=campaign_ids)
                    except Profile.DoesNotExist:
                        return Response({'error': 'Client is not associated with the arbitrator'}, 
                                        status=status.HTTP_400_BAD_REQUEST)
                else:
                    # Arbitrator can only see campaigns for disputes they're assigned to
                    arbitrator_profile = Profile.objects.get(user=user, profile_type=ProfileType.arbitrator.name)
                    # Get all disputes assigned to this arbitrator
                    arbitrator_disputes = Dispute.objects.filter(arbitrator_rv=arbitrator_profile)
                    # Get campaigns associated with these disputes
                    campaign_ids = arbitrator_disputes.values_list('campaign_id', flat=True).distinct()
                    campaigns_queryset = campaigns_queryset.filter(id__in=campaign_ids)

            # # Additional filtering by client_id if provided
            # if user_id:
            #     campaigns_queryset = campaigns_queryset.filter(client_id=user_id)
            # # Apply profile filter if provided (for admin users)
            # elif user_id and is_admin:
            #     try:
            #         profile_id = Profile.objects.get(user_id=user_id).id
            #         if not profile_id:
            #             return Response({'error': 'Invalid client_id'}, status=status.HTTP_400_BAD_REQUEST)
            #         campaigns_queryset = campaigns_queryset.filter(client_id=profile_id)
            #     except Profile.DoesNotExist:
            #         return Response({'error': 'Profile not found for the given client_id'}, status=status.HTTP_400_BAD_REQUEST)

            # Get total counts
            total_campaigns = campaigns_queryset.count()

            # Check if only the latest record is requested
            if count and int(count) == 1:
                recent_campaigns = campaigns_queryset.order_by('-created_at')[:1]
            else:
                # Calculate pagination offsets
                start_index = (page - 1) * page_size
                end_index = start_index + page_size

                # Get campaigns with pagination
                recent_campaigns = campaigns_queryset.order_by('-created_at')[start_index:end_index]

            # Prepare campaign data
            recent_campaign_data = []
            for campaign in recent_campaigns:
                # Get template info
                templates = Template.objects.filter(profile=campaign.client)
                template_data = []

                for template in templates:
                    template_info = {
                        'id': template.id,
                        'name': template.name,
                        'created_at': template.created_at.isoformat() if template.created_at else None,
                        'pdf_file_uploaded': template.pdf_file_uploaded,
                        'renaming_and_splitting_done': template.renaming_and_splitting_done
                    }

                    # Add WhatsApp template info if exists
                    try:
                        whatsapp_template = template.whatsapp_template
                        if whatsapp_template:
                            template_info['whatsapp_template'] = {
                                'id': whatsapp_template.id,
                                'name': whatsapp_template.name,
                                'sent_to_whatsapp': whatsapp_template.sent_to_whatsapp,
                                'whatsapp_status': whatsapp_template.whatsapp_status,
                                'whatsapp_messages_sent': whatsapp_template.whatsapp_messages_sent
                            }
                    except (WhatsAppTemplate.DoesNotExist, AttributeError):
                        pass

                    # Add Email template info if exists
                    try:
                        email_template = template.email_template
                        if email_template:
                            template_info['email_template'] = {
                                'id': email_template.id,
                                'name': email_template.name,
                                'sent_to_email': email_template.sent_to_email,
                                'email_status': email_template.email_status,
                                'email_messages_sent': email_template.email_messages_sent
                            }
                    except (EmailTemplate.DoesNotExist, AttributeError):
                        pass

                    template_data.append(template_info)

                # Get case manager and arbitrator info
                case_manager_status = 'Not Assigned'
                if campaign.case_managers_assigned:
                    case_manager_status = 'Assigned'

                arbitrator_status = 'Not Assigned'
                if campaign.arbitrators_assigned:
                    arbitrator_status = 'Assigned'

                # Prepare campaign info
                campaign_info = {
                    'id': campaign.id,
                    'excel_file_name': campaign.excel_file_name,
                    'excel_display_name':campaign.excel_file_name.split('/')[-1],
                    'excel_file_uploaded': campaign.excel_file_uploaded,
                    'created_at': campaign.created_at.isoformat() if campaign.created_at else None,
                    'case_creation_status': campaign.case_creation_status,
                    'total_cases': campaign.total_rows,
                    'processed_rows': campaign.processed_rows,
                    'number_of_cases_created': campaign.number_of_cases_created,
                    'case_manager_status': case_manager_status,
                    'case_manager_assigned_at': campaign.assigned_case_managers_at.isoformat() if campaign.assigned_case_managers_at else None,
                    'arbitrator_status': arbitrator_status,
                    'arbitrator_assigned_at': campaign.assigned_arbitrators_at.isoformat() if campaign.assigned_arbitrators_at else None,
                    'templates': template_data,
                    'templates_count': len(template_data)
                }

                # Add client info if available
                if campaign.client:
                    try:
                        client_name = f"{campaign.client.user.first_name} {campaign.client.user.last_name}"
                        campaign_info['client'] = {
                            'id': campaign.client.id,
                            'name': client_name
                        }
                    except (AttributeError, TypeError):
                        pass

                # Add created_by info if available
                if campaign.created_by:
                    try:
                        created_by_name = f"{campaign.created_by.first_name} {campaign.created_by.last_name}"
                        campaign_info['created_by'] = {
                            'id': campaign.created_by.id,
                            'name': created_by_name
                        }
                    except (AttributeError, TypeError):
                        pass

                recent_campaign_data.append(campaign_info)

            # For now, sheet uploads data is the same as bulk actions
            sheet_uploads_data = {
                'total_uploads': total_campaigns,
                'recent_uploads': recent_campaign_data
            }

            # Add pagination info
            pagination_info = {
                'page': page,
                'page_size': page_size,
                'total_pages': math.ceil(total_campaigns / page_size) if page_size > 0 else 0,
                'total_items': total_campaigns
            }

            response_data = {
                'sheet_uploads': sheet_uploads_data,
                'pagination': pagination_info
            }

            return Response(response_data)
        except Exception as e:
            db_logger.exception(f"Error in DashboardBulkActionsView.get: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_campaign_detail(self, request, campaign_id):
        """Get detailed data for a specific campaign."""
        try:
            # Check user's profile type
            user = request.user
            is_admin = Profile.objects.filter(user=user, profile_type=ProfileType.admin.name).exists()
            is_client = Profile.objects.filter(user=user, profile_type=ProfileType.client.name).exists()
            is_sub_client = Profile.objects.filter(user=user, profile_type=ProfileType.sub_client.name).exists()
            is_case_manager = Profile.objects.filter(user=user, profile_type=ProfileType.case_manager.name).exists()
            is_arbitrator = Profile.objects.filter(user=user, profile_type=ProfileType.arbitrator.name).exists()

            # Get campaign
            try:
                campaign = Campaign.objects.get(id=campaign_id)
            except Campaign.DoesNotExist:
                return Response({'error': 'Invalid campaign_id'}, status=status.HTTP_400_BAD_REQUEST)

            # Check permissions based on user's profile type
            if is_admin:
                # Admin can access any campaign
                pass
            elif is_client or is_sub_client:
                # Client and sub_client can only access their campaigns
                client_profile = Profile.objects.get(user=user, profile_type__in=[ProfileType.client.name, ProfileType.sub_client.name])
                if not campaign.client or campaign.client.id != client_profile.id:
                    return Response({'error': 'You do not have permission to access this campaign'},
                                   status=status.HTTP_403_FORBIDDEN)
            elif is_case_manager:
                # Case manager can only access campaigns for disputes they're assigned to
                case_manager_profile = Profile.objects.get(user=user, profile_type=ProfileType.case_manager.name)
                # Check if any disputes in this campaign are assigned to this case manager
                campaign_disputes = Dispute.objects.filter(campaign=campaign)
                has_access = False
                for dispute in campaign_disputes:
                    if case_manager_profile in dispute.case_manager_rv.all():
                        has_access = True
                        break
                if not has_access:
                    return Response({'error': 'You do not have permission to access this campaign'},
                                   status=status.HTTP_403_FORBIDDEN)
            elif is_arbitrator:
                # Arbitrator can only access campaigns for disputes they're assigned to
                arbitrator_profile = Profile.objects.get(user=user, profile_type=ProfileType.arbitrator.name)
                # Check if any disputes in this campaign are assigned to this arbitrator
                campaign_disputes = Dispute.objects.filter(campaign=campaign)
                has_access = False
                for dispute in campaign_disputes:
                    if arbitrator_profile in dispute.arbitrator_rv.all():                
                        has_access = True
                        break
                if not has_access:
                    return Response({'error': 'You do not have permission to access this campaign'},
                                   status=status.HTTP_403_FORBIDDEN)

            # Get template info
            templates = Template.objects.filter(profile=campaign.client)
            template_data = []

            for template in templates:
                template_info = {
                    'id': template.id,
                    'name': template.name,
                    'created_at': template.created_at.isoformat() if template.created_at else None,
                    'pdf_file_name': template.pdf_file_name,
                    'pdf_file_uploaded': template.pdf_file_uploaded,
                    'renaming_and_splitting_done': template.renaming_and_splitting_done
                }

                # Add WhatsApp template info if exists
                try:
                    whatsapp_template = template.whatsapp_template
                    if whatsapp_template:
                        template_info['whatsapp_template'] = {
                            'id': whatsapp_template.id,
                            'template_id': whatsapp_template.whatsapp_template_id,
                            'sent_to_whatsapp': whatsapp_template.sent_to_whatsapp,
                            'whatsapp_status': whatsapp_template.whatsapp_status,
                            'whatsapp_messages_sent': whatsapp_template.whatsapp_messages_sent,
                            'whatsapp_processed_rows': whatsapp_template.whatsapp_processed_rows,
                        }
                except (WhatsAppTemplate.DoesNotExist, AttributeError):
                    pass

                # Add Email template info if exists
                try:
                    email_template = template.email_template
                    if email_template:
                        template_info['email_template'] = {
                            'id': email_template.id,
                            'template_id': email_template.email_template_id,
                            'sent_to_email': email_template.sent_to_email,
                            'email_status': email_template.email_status,
                            'email_messages_sent': email_template.email_messages_sent,
                            'email_processed_rows': email_template.email_processed_rows,
                        }
                except Exception as e:
                    db_logger.error(f"Error fetching Email template for template {template.id}: {str(e)}")

                template_data.append(template_info)

            # Get case manager and arbitrator info
            case_manager_status = 'Not Assigned'
            if campaign.case_managers_assigned:
                case_manager_status = 'Assigned'
                case_manager_assigned_at = campaign.assigned_case_managers_at.isoformat() if campaign.assigned_case_managers_at else None
            else:
                case_manager_assigned_at = None

            arbitrator_status = 'Not Assigned'
            if campaign.arbitrators_assigned:
                arbitrator_status = 'Assigned'
                arbitrator_assigned_at = campaign.assigned_arbitrators_at.isoformat() if campaign.assigned_arbitrators_at else None
            else:
                arbitrator_assigned_at = None

            # Prepare campaign info
            campaign_info = {
                'id': campaign.id,
                'excel_file_name': campaign.excel_file_name,
                'excel_display_name':campaign.excel_file_name.split('/')[-1],
                'excel_file_uploaded': campaign.excel_file_uploaded,
                'created_at': campaign.created_at.isoformat() if campaign.created_at else None,
                'updated_at': campaign.updated_at.isoformat() if campaign.updated_at else None,
                'status': campaign.case_creation_status,
                'total_rows': campaign.total_rows,
                'processed_rows': campaign.processed_rows,
                'number_of_cases_created': campaign.number_of_cases_created,
                'case_manager_status': case_manager_status,
                'case_manager_assigned_at': case_manager_assigned_at,
                'arbitrator_status': arbitrator_status,
                'arbitrator_assigned_at': arbitrator_assigned_at,
                'templates': template_data,
                'templates_count': len(template_data),
                'processing_errors': campaign.processing_errors,
                'case_creation_report_generated': campaign.case_creation_report_generated,
                'case_creation_report_file_path': campaign.case_creation_report_file_path
            }

            # Calculate success rate
            if campaign.total_rows > 0:
                campaign_info['success_rate'] = round((campaign.number_of_cases_created / campaign.total_rows) * 100, 2)
            else:
                campaign_info['success_rate'] = 0

            # Add client info if available
            if campaign.client:
                try:
                    client_name = f"{campaign.client.user.first_name} {campaign.client.user.last_name}"
                    campaign_info['client'] = {
                        'id': campaign.client.id,
                        'name': client_name
                    }
                except (AttributeError, TypeError):
                    pass

            # Add created_by info if available
            if campaign.created_by:
                try:
                    created_by_name = f"{campaign.created_by.first_name} {campaign.created_by.last_name}"
                    campaign_info['created_by'] = {
                        'id': campaign.created_by.id,
                        'name': created_by_name
                    }
                except (AttributeError, TypeError):
                    pass

            return Response(campaign_info)
        except Exception as e:
            db_logger.exception(f"Error in DashboardBulkActionsView.get_campaign_detail: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CampaignTotalCasesView(APIView):
    """API to get total cases created for a campaign."""
    permission_classes = [IsAuthenticated, IsAnyRole]

    def get(self, request, campaign_id=None):
        """Get total cases created for a campaign."""
        try:
            if not campaign_id:
                return Response({'error': 'campaign_id is required'}, status=status.HTTP_400_BAD_REQUEST)

            # Check user's profile type
            user = request.user
            current_profile = get_object_or_404(Profile,user=user)
            
            # Get campaign details
            try:
                campaign = Campaign.objects.get(id=campaign_id)
            except Campaign.DoesNotExist:
                return Response({'error': 'Invalid campaign_id'}, status=status.HTTP_400_BAD_REQUEST)

            # Check permissions based on user's profile type
            if not validate_profile_campaign(current_profile,campaign):
                    return Response({'error': 'You do not have permission to access this campaign'},
                                   status=status.HTTP_403_FORBIDDEN)           

            # Get total cases created
            total_cases = campaign.number_of_cases_created

            return Response({'total_cases': total_cases})
        except Exception as e:
            db_logger.exception(f"Error in CampaignTotalCasesView.get: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DisputeNoticeDetailsView(APIView):
    """API to get notice details and timeline for a dispute."""
    permission_classes = [IsAuthenticated, IsAnyRole]

    def get(self, request, dispute_id=None):
        """Get notice details and timeline for a dispute."""
        try:
            if not dispute_id:
                return Response({'error': 'dispute_id is required'}, status=status.HTTP_400_BAD_REQUEST)

            # Check user's profile type
            user = request.user
            current_profile = get_object_or_404(Profile,user=user)                       
            dispute = get_object_or_404(Dispute, id=dispute_id)

            if not validate_profile_dispute(current_profile,dispute):
                    return Response({'error': 'You do not have permission to access this dispute'},
                                   status=status.HTTP_403_FORBIDDEN)            

            notices = Notice.objects.filter(dispute_id=dispute_id).order_by('-created_at')

            if not notices.exists():
                return Response({'error': 'No notices found for this dispute'}, status=status.HTTP_400_BAD_REQUEST)

            timeline_events = []

            for notice in notices:
                template = notice.template
                template_info = {}

                if template:
                    template_info = {
                        'template_id': template.template_id,
                        'template_name': template.name,
                        'created_at': template.created_at.isoformat() if template.created_at else None,
                    }

                    # Check if WhatsApp was actually sent (using MasterExcelExportView logic)
                    whatsapp_sent_timestamp, whatsapp_read_timestamp = self._get_whatsapp_read_time(dispute, template.id)
                    if whatsapp_sent_timestamp:
                        template_info.update({
                            'whatsapp_template_id': template.whatsapp_template.whatsapp_template_id if template.whatsapp_template else None,
                            'whatsapp_sent_on': whatsapp_sent_timestamp,
                            'whatsapp_read_on': whatsapp_read_timestamp
                        })

                    # Check if Email was actually sent (using MasterExcelExportView logic)
                    email_sent_timestamp, email_read_timestamp = self._get_email_read_time(dispute, template.id)
                    if email_sent_timestamp:
                        template_info.update({
                            'email_template_id': template.email_template.email_template_id if template.email_template else None,
                            'email_template_name': template.email_template.name if template.email_template else None,
                            'email_sent_on': email_sent_timestamp,
                            'email_read_on': email_read_timestamp,
                        })

                # Always add notice information regardless of template existence
                template_info.update({
                    'notice_id': notice.id,
                    'notice_name': notice.file_name,
                    'notice_type': template.name if template else None,
                    'template_id': template.template_id if template else None,
                    'template_name': template.name if template else None,
                    'created_on': notice.created_at.isoformat() if notice.created_at else None
                })
                timeline_events.append(template_info)

            timeline_events.sort(key=lambda x: x.get('created_on', ''), reverse=True)

            response_data = {
                'dispute_id': dispute_id,
                'timeline': timeline_events
            }

            return Response(response_data)
        except Exception as e:
            db_logger.exception(f"Error in DisputeNoticeDetailsView.get: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_loan_id(self, notice):
        try:
            dispute = notice.dispute
            if hasattr(dispute, 'loan_id'):
                return dispute.loan_id
            elif hasattr(dispute, 'case') and hasattr(dispute.case, 'loan_id'):
                return dispute.case.loan_id
        except (AttributeError, TypeError):
            pass
        return None

    def _get_whatsapp_read_time(self, dispute, template_id):
        """Get WhatsApp initiated and read timestamp"""
        try:
            initiated_entry = Entry.objects.filter(
                dispute=dispute, template_id=template_id, status='initiated'
            ).order_by('-timestamp').first()

            if initiated_entry:
                read_entry = Entry.objects.filter(
                    msg_id=initiated_entry.msg_id, status='read'
                ).order_by('-timestamp').first()

                if read_entry:
                    return initiated_entry.timestamp.isoformat(), read_entry.timestamp.isoformat()
                return initiated_entry.timestamp.isoformat(), None
            return None, None
        except Exception:
            return None, None

    def _get_email_read_time(self, dispute, template_id):
        """Get email initiated and read timestamp"""
        try:
            email_entry = SendgridMail.objects.filter(
                dispute=dispute, template_id=template_id
            ).order_by('-timestamp').first()

            if email_entry:
                read_entry = SendgridEventEntry.objects.filter(
                    sendgrid_mail=email_entry, event='open'
                ).order_by('-timestamp').first()

                if read_entry:
                    return email_entry.timestamp.isoformat(), read_entry.timestamp.isoformat()
                return email_entry.timestamp.isoformat(), None
            return None, None
        except Exception:
            return None, None

def campaign_message_sent_progress_stream(request, campaign_id, template_id, message_type):
    """
    Server-Sent Events (SSE) stream for WhatsApp/Email message sending progress.

    This function provides real-time updates on message sending progress by checking
    the database at regular intervals and sending updates when changes are detected.
    """
    def event_stream():
        last_update = None
        consecutive_errors = 0
        max_consecutive_errors = 5

        # Log start of streaming for debugging
        print(f"Starting progress stream for campaign {campaign_id}, template {template_id}, type {message_type}")

        while True:
            try:
                # Get campaign and template
                campaign = Campaign.objects.get(id=campaign_id)
                template = Template.objects.filter(id=template_id).first()

                if not template:
                    print(f"Template {template_id} not found")
                    yield f"data: {json.dumps({'error': 'Template not found'})}\n\n"
                    break

                # Get the total from the campaign's total rows
                total_rows = campaign.total_rows
                errors = []

                # Get the appropriate message template based on type
                if message_type == 'whatsapp':
                    message_template = template.whatsapp_template
                    if not message_template:
                        print(f"WhatsApp template not found for template {template_id}")
                        yield f"data: {json.dumps({'error': 'WhatsApp template not found'})}\n\n"
                        break

                    template_obj = WhatsAppTemplate.objects.filter(id=message_template.id).first()
                    if not template_obj:
                        print(f"WhatsApp template object {message_template.id} not found")
                        yield f"data: {json.dumps({'error': 'WhatsApp template object not found'})}\n\n"
                        break

                    processed_rows = template_obj.whatsapp_processed_rows or 0

                    # For messages_sent, we need the total messages sent for this campaign
                    # This should be the sum of all messages sent for all templates in this campaign
                    messages_sent = 0

                    # First include this template's messages sent
                    messages_sent += template_obj.whatsapp_messages_sent or 0

                    # Add any errors from the template if available
                    if hasattr(template_obj, 'whatsapp_errors') and template_obj.whatsapp_errors:
                        try:
                            if isinstance(template_obj.whatsapp_errors, list):
                                errors = template_obj.whatsapp_errors
                            elif isinstance(template_obj.whatsapp_errors, str):
                                errors = [template_obj.whatsapp_errors]
                            else:
                                errors = []
                        except:
                            errors = []

                    status = template_obj.whatsapp_status or 'pending'

                elif message_type == 'email':
                    message_template = template.email_template
                    if not message_template:
                        print(f"Email template not found for template {template_id}")
                        yield f"data: {json.dumps({'error': 'Email template not found'})}\n\n"
                        break

                    template_obj = EmailTemplate.objects.filter(id=message_template.id).first()
                    if not template_obj:
                        print(f"Email template object {message_template.id} not found")
                        yield f"data: {json.dumps({'error': 'Email template object not found'})}\n\n"
                        break

                    processed_rows = template_obj.email_processed_rows or 0

                    # For messages_sent, we need the total messages sent for this campaign
                    messages_sent = 0

                    # First include this template's messages sent
                    messages_sent += template_obj.email_messages_sent or 0

                    # Add any errors from the template if available
                    if hasattr(template_obj, 'email_errors') and template_obj.email_errors:
                        try:
                            if isinstance(template_obj.email_errors, list):
                                errors = template_obj.email_errors
                            elif isinstance(template_obj.email_errors, str):
                                errors = [template_obj.email_errors]
                            else:
                                errors = []
                        except:
                            errors = []

                    status = template_obj.email_status or 'pending'

                else:
                    print(f"Invalid message_type: {message_type}")
                    yield f"data: {json.dumps({'error': 'Invalid message_type. Must be whatsapp or email.'})}\n\n"
                    break

                # Create the current state object with the format matching the renaming/splitting format
                current_state = {
                    'processed': processed_rows,
                    'messages_sent': messages_sent,
                    'total': total_rows,
                    'status': status,
                    'errors': errors
                }

                # Debug log to help diagnose issues
                print(f"Current state: processed={processed_rows}, messages_sent={messages_sent}, " +
                      f"total={total_rows}, status={status}, errors_count={len(errors)}")

                # Only send updates when there are changes
                if current_state != last_update:
                    print(f"Sending update: {json.dumps(current_state)}")
                    yield f"data: {json.dumps(current_state)}\n\n"
                    last_update = current_state

                # Check if we need to stop streaming
                if status in ['completed', 'failed']:
                    print(f"Stopping stream - final status: {status}")
                    break

                # Reset error counter on successful iteration
                consecutive_errors = 0

                # Wait before checking again
                time.sleep(1)  # Check every second

            except Exception as e:
                # Log error but continue streaming unless too many consecutive errors
                print(f"Error in campaign_message_sent_progress_stream: {str(e)}")
                consecutive_errors += 1

                if consecutive_errors >= max_consecutive_errors:
                    print(f"Too many consecutive errors ({consecutive_errors}), stopping stream")
                    yield f"data: {json.dumps({'error': f'Stream interrupted: {str(e)}'})}\n\n"
                    break

                # Wait a bit longer on error
                time.sleep(2)

    # Return streaming response
    response = StreamingHttpResponse(event_stream(), content_type='text/event-stream')
    # Add headers to prevent caching
    response['Cache-Control'] = 'no-cache'
    response['X-Accel-Buffering'] = 'no'
    return response


class SendWhatsAppTest(APIView):
    def post(self, request):
        try:
            mobile = request.data.get('mobile')
            template_id = request.data.get('template_id')

            if not mobile or not template_id:
                return Response(
                    {"error": "Both 'mobile' and 'template_id' are required."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Fetch template details
            template = Template.objects.get(id=template_id)
            if not template:
                return Response({'error': 'Template with given id not found.'}, status=status.HTTP_400_BAD_REQUEST)

            whatsapp_template = template.whatsapp_template
            if not whatsapp_template:
                return Response({'error': 'WhatsApp template not found for the given template.'}, status=status.HTTP_400_BAD_REQUEST)

            # Check conciliation type directly from model
            is_conciliation = any([
                whatsapp_template.conciliation_notice_1,
                whatsapp_template.conciliation_notice_2,
                whatsapp_template.conciliation_notice_3,
                whatsapp_template.conciliation_notice_4
            ])

            client_name = User.objects.filter(id=template.profile.user_id).first().first_name
            media_id = None
            file_name = None

            # Handle document upload if needed
            if whatsapp_template.requires_attachment:
                file_path = os.path.join(BASE_DIR, "temp", "test.pdf")
                try:
                    with open(file_path, "rb") as f:
                        file_data = f.read()
                    upload_response = upload_media_to_whatsapp(file_data, "test.pdf", whatsapp_template.is_s21_notice)
                    if upload_response and upload_response.get('id'):
                        media_id = upload_response['id']
                        file_name = "test.pdf"
                    else:
                        return Response({"error": "Failed to upload document to WhatsApp."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                except FileNotFoundError:
                    return Response({"error": f"File not found at path {file_path}."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Send message
            if is_conciliation:
                # Get parameters based on which conciliation type is true
                parameters = get_default_parameters(whatsapp_template, client_name)
                response = send_whatsapp_message_test(
                    mobile=mobile,
                    template_id=whatsapp_template.whatsapp_template_id,
                    is_s21_notice=whatsapp_template.is_s21_notice,
                    parameters=parameters,
                    media_id=media_id,
                    file_name=file_name,
                    lang_code=whatsapp_template.lang_code or "en_US",
                    has_body_params=True
                )
            else:
                # Regular message
                response = send_whatsapp_message_test(
                    mobile=mobile,
                    template_id=whatsapp_template.whatsapp_template_id,
                    is_s21_notice=whatsapp_template.is_s21_notice,
                    body_param_text=client_name,
                    media_id=media_id,
                    file_name=file_name,
                    lang_code=whatsapp_template.lang_code or "en",
                    has_body_params=whatsapp_template.has_body_params
                )

            return Response({
                "message": "Message sent successfully",
                "whatsapp_response": response,
                "is_conciliation_notice": is_conciliation
            })

        except Exception as e:
            db_logger.exception(f"Error in SendWhatsAppMessage.post: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def get_default_parameters(whatsapp_template, client_name):
    """Get default test parameters based on template conciliation type"""
    if whatsapp_template.conciliation_notice_1:
        return ["TEST123456", client_name, "25-01-2025", "10:00 AM", "11:00 AM", "https://meet.test.com"]
    elif whatsapp_template.conciliation_notice_2:
        return [client_name, "25-01-2025", "10:00 AM", "11:00 AM", "https://meet.test.com"]
    elif whatsapp_template.conciliation_notice_3:
        return ["25-01-2025", client_name, "25-01-2025", "10:00 AM", "11:00 AM", "https://meet.test.com"]
    elif whatsapp_template.conciliation_notice_4:
        return [client_name, "https://meet.test.com"]
    return []


class SendEmailTest(APIView):
    permission_classes = [IsAuthenticated, IsAnyRole]
    def post(self, request):
        try:
            # Extract email and template ID from the request data
            email = request.data.get('email')
            template_id = request.data.get('template_id')

            if not email or not template_id:
                return Response({'error': 'Email and template_id are required.'}, status=status.HTTP_400_BAD_REQUEST)

            # Check user's profile type
            user = request.user
            current_profile = Profile.objects.get(user=user)
            current_profile_type = current_profile.profile_type

            is_admin=False
            is_client=False
            is_sub_client=False
            is_case_manager=False
            is_arbitrator=False
            
            if current_profile_type == ProfileType.admin.name:
                is_admin=True
            elif current_profile_type == ProfileType.client.name:
                is_client=True
            elif current_profile_type == ProfileType.sub_client.name:
                is_sub_client=True
            elif current_profile_type == ProfileType.case_manager.name:
                is_case_manager=True
            elif current_profile_type == ProfileType.arbitrator.name:
                is_arbitrator = True

            # Only admin, client, sub_client, and case_manager can send test emails
            if not (is_admin or is_client or is_sub_client or is_case_manager):
                return Response(
                    {"error": "You do not have permission to send test emails."},
                    status=status.HTTP_403_FORBIDDEN
                )

            template = Template.objects.get(id=template_id)
            if not template:
                return Response({'error': 'Template with given id not found.'}, status=status.HTTP_400_BAD_REQUEST)

            email_template = template.email_template
            if not email_template:
                return Response({'error': 'Email template not found for the given template.'}, status=status.HTTP_400_BAD_REQUEST)

            client_name = User.objects.filter(id=template.profile.user_id).first().first_name
            substition_data = {'client_name': client_name}

            # Path to the PDF file to attach
            attachment_path = os.path.join(BASE_DIR, "temp", "test.pdf")

            # Read the file content
            with open(attachment_path, "rb") as file:
                file_content = file.read()

            # Prepare attachment data
            attachments = [{
                "file": file_content,
                "name": "test.pdf"
            }]

            response = send_email(
                to_email=email,
                template_id=email_template.email_template_id,
                files=attachments,
                email_template=email_template,
                substition_data=substition_data
            )

            # Check the result and send appropriate responsesend_result
            if response and isinstance(response, str) and ('202' in response or 'success' in response):
                return Response({'message': 'Email sent successfully.'}, status=status.HTTP_200_OK)
            else:
                return Response({'error': f"Failed to send email: {response if isinstance(response, str) else response.get('error', 'Unknown error')}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SendPFLPaymentRequestNotice(APIView):
    def post(self, request):
        email = request.data.get('customer_email_id')
        phone_number = request.data.get('customer_mobile_no')
        borrowers_name = request.data.get('borrower_name')
        notice_date = request.data.get('notice_date')
        notice_amount = request.data.get('notice_amount')
        payment_link = request.data.get('payment_link')
        loan_number = request.data.get('proposal_no')

        # Ensure the fields are not empty or invalid
        if not all([email, phone_number, borrowers_name, notice_date, notice_amount, payment_link, loan_number]):
            return Response({"error": "Missing required fields."}, status=status.HTTP_400_BAD_REQUEST)

        # Prepare output path for the attachment - try both lowercase and uppercase extensions
        output_path_lowercase = f'notices/91/128/Payment Request – Demand Notice issued against to you/{loan_number}.pdf'
        output_path_uppercase = f'notices/91/128/Payment Request – Demand Notice issued against to you/{loan_number}.PDF'

        # Try to read the file with lowercase extension first
        try:
            attachment = gcs_manager.read_file(output_path_lowercase)
            file_path = output_path_lowercase
        except Exception as e:
            # If lowercase fails, try uppercase
            try:
                attachment = gcs_manager.read_file(output_path_uppercase)
                file_path = output_path_uppercase
            except Exception as e2:
                # If both fail, return an error
                return Response(
                    {"error": f"Could not find PDF file for loan {loan_number}. Tried both .pdf and .PDF extensions."},
                    status=status.HTTP_404_NOT_FOUND
                )

        email_files = [{
            'file': attachment,
            'name': f'{loan_number}.pdf'
        }]

        try:
            # Send email if email is available
            if email and email != "nan":  # Check if email is valid
                send_pfl_termination_notice(
                    email=email,
                    files=email_files,
                    loan_number=loan_number,
                    borrowers_name=borrowers_name,
                    client_name='Poonawalla Finance Limited',
                    notice_date=notice_date,
                    notice_amount=notice_amount,
                    payment_link=payment_link
                )
                print(f"email sent to {email}")

            # Send WhatsApp if phone number is available
            if phone_number and phone_number != "nan":  # Check if phone number is valid
                # Create temp file for WhatsApp
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                    temp_file.write(attachment)  # Save the binary content to the temp file
                    temp_file_path = temp_file.name  # Get the path of the temporary file

                try:
                    # Pass the binary content directly instead of the filename
                    # The first parameter should be the filename (as string), and the second parameter should be the binary content
                    response_data = upload_media_to_whatsapp(
                        file_name=f'{loan_number}.pdf',  # This is just the name, not the content
                        file_data=attachment,  # This is the binary content
                        is_s21_notice=True
                    )

                    resp = send_whatsapp_pfl(
                        mobile=phone_number,
                        file_name=f'{loan_number}.pdf',
                        media_id=response_data.get('id'),
                        borrowers_name=borrowers_name,
                        client_name='Poonawalla Finance Limited',
                        notice_date=notice_date,
                        notice_amount=notice_amount,
                        payment_link=payment_link,
                        loan_acc_number=loan_number
                    )
                    if resp:
                        msg_resp = json.loads(resp)
                        # phone_number = msg_resp["contacts"][0]["input"]
                        msg_id = msg_resp["messages"][0]["id"]
                        timestamp = timezone.make_aware(
                            datetime.now(), pytz.timezone(TIME_ZONE))
                        Entry.objects.create(
                            msg_id=msg_id,
                            phone_number=phone_number,
                            status='initiated',
                            timestamp=timestamp,
                            loan_id=loan_number
                        )
                    print(f'whatsapp message sent to {phone_number}')

                finally:
                    if os.path.exists(temp_file_path):
                        os.remove(temp_file_path)

        except Exception as e:
            # Log any errors for further inspection
            logging.error(f"Error processing loan {loan_number} - {e}")
            # Add traceback for better debugging
            logging.error(traceback.format_exc())
        return Response({"message": "Email and WhatsApp sent successfully."}, status=status.HTTP_200_OK)


class CampaignDetailView(APIView):
    permission_classes = [IsAuthenticated, IsAdminOrClient]

    def get(self, request, campaign_id=None):
        logging.info('In get of Campaigndetail view')
        logging.info(f'campaign id is {campaign_id}')
        if campaign_id is None:
            return Response({'error': 'campaign_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        campaign = get_object_or_404(Campaign,id=campaign_id)

        user=request.user
        current_profile = get_object_or_404(Profile,user=user)

        if not validate_profile_campaign(current_profile,campaign):
            return Response({'error': 'You do not have permission to access this campaign'},
                            status=status.HTTP_403_FORBIDDEN)
        
        campaign_serializer = CampaignDetailSerializer(campaign)

        return Response({"campaign": campaign_serializer.data}, status=status.HTTP_200_OK)


class NoticesForCampaignTemplate(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, campaign_id, template_id):
        if not template_id or not campaign_id:
            return Response({"error": "template_id and campaign_id parameters are required."}, status=status.HTTP_400_BAD_REQUEST)

        # Verify campaign exists
        try:
            campaign = Campaign.objects.get(id=campaign_id)
        except Campaign.DoesNotExist:
            return Response({"error": "Campaign not found."}, status=status.HTTP_400_BAD_REQUEST)

        # Verify template exists
        try:
            template = Template.objects.get(id=template_id)
        except Template.DoesNotExist:
            return Response({"error": "Template not found."}, status=status.HTTP_400_BAD_REQUEST)

        # Get all disputes for the campaign
        disputes = Dispute.objects.filter(campaign_id=campaign_id)

        if not disputes.exists():
            return Response({
                "total_pdf_files": 0,
                "notices": [],
                "campaign_id": campaign_id,
                "template_id": template_id
            }, status=status.HTTP_200_OK)
 
        # Get all notices for disputes in this campaign with the specified template
        notices = Notice.objects.filter(
            dispute__campaign_id=campaign_id,
            template_id=template_id
        ).select_related('dispute')

        # Count PDF files (assuming all notices are for PDF files)
        total_pdf_files = notices.count()

        # Prepare response data with only id and file_name
        notices_data = [
            {
                "id": notice.id,
                "file_name": notice.file_name,
                "dispute_id": notice.dispute.id
            }
            for notice in notices
        ]

        return Response({
            "total_pdf_files": total_pdf_files,
            "notices": notices_data,
            "campaign_id": campaign_id,
            "template_id": template_id
        }, status=status.HTTP_200_OK)
